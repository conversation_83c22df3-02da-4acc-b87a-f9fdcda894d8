import React, { useState, useMemo, useEffect } from 'react';
import { <PERSON>, Spinner, Badge, Dropdown, OverlayTrigger, Tooltip } from 'react-bootstrap';
import { ChevronDown, ChevronRight, Info, ArrowDownRight, Download, Eye, Calendar, Database } from 'lucide-react';
import { LayerDiscovery } from '../../types/discovery';
import { discoverLayers, CategoryInfo, CategorizedLayers } from '../../services/discoveryService';
import './DataLayers.css';

interface DataLayersProps {
  layers: LayerDiscovery[];
  selectedLayerNames: string[];
  onLayerChange: (layerName: string) => void;
  isLoading?: boolean;
  error?: string | null;
  selectedBasemap?: string;
  onBasemapChange?: (basemapName: string) => void;
  // Layer information control
  onShowLayerInfo?: (layerName: string) => void;
}

// Dynamic accordion sections - will be populated from discovery

const DataLayers: React.FC<DataLayersProps> = ({
  layers,
  selectedLayerNames,
  onLayerChange,
  isLoading = false,
  error = null,
  selectedBasemap = 'osm:osm', // Default to OpenStreetMap
  onBasemapChange,
  onShowLayerInfo
}) => {

  // Helper function to render layer items with info badges
  const renderLayerItem = (layer: LayerDiscovery, categoryKey: string) => (
    <div key={layer.name} className="layer-item-with-info">
      <div className="layer-checkbox-container">
        <Form.Check
          type="checkbox"
          id={`${categoryKey}-layer-${layer.name}`}
          checked={selectedLayerNames.includes(layer.name)}
          onChange={() => onLayerChange(layer.name)}
          className="layer-checkbox"
          style={{
            fontSize: '0.8rem',
            marginBottom: '0.3rem'
          }}
        />
        <label
          htmlFor={`${categoryKey}-layer-${layer.name}`}
          className="layer-label-with-info"
          style={{ fontSize: '0.8rem', marginLeft: '0.5rem', cursor: 'pointer' }}
        >
          {layer.title || layer.name}
          {(layer as any).isRemote && (
            <Badge
              bg="warning"
              className="ms-1"
              style={{ fontSize: '0.6rem' }}
              title="Remote/External Layer"
            >
              Remote
            </Badge>
          )}
          {selectedLayerNames.includes(layer.name) && (
            <Dropdown drop="down" align="start">
              <Dropdown.Toggle
                variant="secondary"
                size="sm"
                className="layer-options-toggle"
                style={{
                  fontSize: '0.6rem',
                  marginLeft: '0.5rem',
                  padding: '0.15rem',
                  width: '16px',
                  height: '16px',
                  borderRadius: '3px',
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: '#6c757d',
                  color: 'white',
                  border: 'none'
                }}
                title="Layer options"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                <ArrowDownRight size={8} />
              </Dropdown.Toggle>

              <Dropdown.Menu className="layer-options-menu">
                <Dropdown.Item
                  onClick={(e) => {
                    e.stopPropagation();
                    console.log('Metadata clicked for:', layer.name);
                    onShowLayerInfo?.(layer.name);
                  }}
                >
                  <Info size={12} className="me-2" />
                  Metadata
                </Dropdown.Item>

                <Dropdown.Item
                  onClick={(e) => {
                    e.stopPropagation();
                    console.log('Download layer:', layer.name);
                    // Add download functionality
                  }}
                >
                  <Download size={12} className="me-2" />
                  Download
                </Dropdown.Item>

                <Dropdown.Item
                  onClick={(e) => {
                    e.stopPropagation();
                    console.log('View details:', layer.name);
                    // Add view details functionality
                  }}
                >
                  <Eye size={12} className="me-2" />
                  View Details
                </Dropdown.Item>

                {(layer as any).temporal?.hasTemporal && (
                  <Dropdown.Item
                    onClick={(e) => {
                      e.stopPropagation();
                      console.log('Temporal controls:', layer.name);
                      // Add temporal controls
                    }}
                  >
                    <Calendar size={12} className="me-2" />
                    Time Controls
                  </Dropdown.Item>
                )}

                <Dropdown.Item
                  onClick={(e) => {
                    e.stopPropagation();
                    console.log('Data source:', layer.name);
                    // Add data source info
                  }}
                >
                  <Database size={12} className="me-2" />
                  Data Source
                </Dropdown.Item>
              </Dropdown.Menu>
            </Dropdown>
          )}
        </label>
      </div>
    </div>
  );

  const [openSections, setOpenSections] = useState<string[]>([]); // All collapsed by default

  // Dynamic discovery state
  const [discoveryCategories, setDiscoveryCategories] = useState<CategoryInfo[]>([]);
  const [discoveryLayers, setDiscoveryLayers] = useState<LayerDiscovery[]>([]);
  const [discoveryCategorized, setDiscoveryCategorized] = useState<CategorizedLayers>({});
  const [discoveryLoading, setDiscoveryLoading] = useState(false);
  const [discoveryError, setDiscoveryError] = useState<string | null>(null);

  // Perform discovery on component mount
  useEffect(() => {
    const performDiscovery = async () => {
      setDiscoveryLoading(true);
      setDiscoveryError(null);

      try {
        console.log('DataLayers: Starting layer discovery...');
        const discovery = await discoverLayers();

        setDiscoveryCategories(discovery.categories);
        setDiscoveryLayers(discovery.layers);
        setDiscoveryCategorized(discovery.categorized);

        console.log(`DataLayers: Discovery completed - ${discovery.layers.length} layers, ${discovery.categories.length} categories`);
      } catch (error: any) {
        console.error('DataLayers: Discovery failed:', error);
        setDiscoveryError(error.message);
      } finally {
        setDiscoveryLoading(false);
      }
    };

    performDiscovery();
  }, []); // Run once on mount

  // Use discovery categorized layers only - no fallback to old categorization
  const categorizedLayers: CategorizedLayers = useMemo(() => {
    if (Object.keys(discoveryCategorized).length > 0) {
      console.log('DataLayers: Using discovery categorization:', Object.keys(discoveryCategorized).length, 'categories');
      return discoveryCategorized;
    } else {
      // Return empty categorization until discovery completes
      console.log('🔍 Waiting for discovery to complete...');
      return {};
    }
  }, [discoveryCategorized]);

  // Function to get layer count for each category (discovery only)
  const getLayerCount = (categoryKey: string) => {
    return categorizedLayers[categoryKey]?.length || 0;
  };

  const toggleSection = (key: string) => {
    setOpenSections((prev: string[]) =>
      prev.includes(key) ? prev.filter((k) => k !== key) : [...prev, key]
    );
  };

  return (
    <div className="data-layers section">
      {isLoading && (
        <div className="section-loading mb-3">
          <Spinner animation="border" size="sm" className="me-2" />
          <small className="text-muted">Discovering layers...</small>
        </div>
      )}
      {error && (
        <div className="section-error mb-3">
          <small className="text-danger">{error}</small>
        </div>
      )}
      {/* Show discovery loading state */}
      {discoveryLoading && (
        <div className="text-center py-3">
          <Spinner animation="border" size="sm" className="me-2" />
          <small className="text-muted">Discovering layers...</small>
        </div>
      )}

      {/* Show discovery error */}
      {discoveryError && (
        <div className="text-center py-2">
          <small className="text-danger">Discovery failed: {discoveryError}</small>
        </div>
      )}

      {/* Show message when no categories discovered yet */}
      {!discoveryLoading && !discoveryError && discoveryCategories.length === 0 && (
        <div className="text-center py-3">
          <small className="text-muted">No layer categories discovered yet</small>
        </div>
      )}

      {/* Render dynamic accordion sections based on discovered categories only */}
      {discoveryCategories.length > 0 && discoveryCategories.map(({ key, title, description, count }) => (
        <div key={key} className="custom-accordion-section">
          <div
            className="custom-accordion-header"
            onClick={() => toggleSection(key)}
            style={{
              cursor: 'pointer',
              fontWeight: 500,
              fontSize: '0.9rem',
              background: '#1e4080', // Same blue as other buttons
              color: 'white',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              borderRadius: openSections.includes(key) ? '6px 6px 0 0' : '6px', // Curved corners
              padding: '0.4rem 0.8rem',
              marginBottom: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              transition: 'background-color 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#0f2d5c';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#1e4080';
            }}
          >
            <div className="d-flex align-items-center">
              {openSections.includes(key) ? (
                <ChevronDown size={14} className="me-2" />
              ) : (
                <ChevronRight size={14} className="me-2" />
              )}
              <span>{title}</span>
            </div>
            <Badge
              bg={count > 0 ? "success" : "secondary"}
              className="ms-2"
              style={{ fontSize: '0.7rem' }}
            >
              {count}
            </Badge>
          </div>
          {openSections.includes(key) && (
            <div
              className="custom-accordion-body"
              style={{
                border: '1px solid rgba(255, 255, 255, 0.2)',
                borderTop: 'none',
                borderRadius: '0 0 6px 6px', // Curved bottom corners
                padding: '0.6rem 0.8rem',
                marginBottom: '0.8rem',
                background: 'rgba(255, 255, 255, 0.05)',
                fontSize: '0.85rem'
              }}
            >
              {/* Dynamic content based on category key */}
              {key === 'basemaps' ? (
                <Form>
                  {/* Dynamic basemap layers from discovery */}
                  {categorizedLayers.basemaps && categorizedLayers.basemaps.length > 0 ? (
                    categorizedLayers.basemaps.map(layer => (
                      <Form.Check
                        key={layer.name}
                        type="radio"
                        name="basemap-selection"
                        id={`basemap-layer-${layer.name}`}
                        label={layer.title || layer.name}
                        checked={selectedBasemap === layer.name}
                        onChange={() => onBasemapChange?.(layer.name)}
                        className="layer-checkbox"
                        style={{
                          fontSize: '0.8rem',
                          marginBottom: '0.3rem'
                        }}
                      />
                    ))
                  ) : (
                    <div className="text-muted text-center py-2">
                      <small>No basemap layers available from discovery</small>
                    </div>
                  )}
                  {/* Always include OpenStreetMap as fallback */}
                  <Form.Check
                    key="osm"
                    type="radio"
                    name="basemap-selection"
                    id="basemap-osm"
                    label="OpenStreetMap"
                    checked={selectedBasemap === 'osm:osm'}
                    onChange={() => onBasemapChange?.('osm:osm')}
                    className="layer-checkbox"
                    style={{
                      fontSize: '0.8rem',
                      marginBottom: '0.3rem'
                    }}
                  />
                  <div className="mt-2">
                    <small className="text-muted">
                      Basemaps are discovered dynamically from GeoServer
                    </small>
                  </div>
                </Form>

              ) : (
                <div>
                  {/* Generic dynamic content for all other categories */}
                  {categorizedLayers[key] && categorizedLayers[key].length > 0 ? (
                    categorizedLayers[key].map((layer: LayerDiscovery) => renderLayerItem(layer, key))
                  ) : (
                    <div className="text-muted text-center py-2">
                      <small>No {title.toLowerCase()} available</small>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default DataLayers;