FROM node:20-alpine

WORKDIR /app

# Add security updates and required packages
RUN apk add --no-cache --update curl && \
    apk upgrade && \
    rm -rf /var/cache/apk/*

# Copy package files first for better caching
COPY uiengine/package*.json ./

# Install dependencies with security audit
RUN npm ci --only=production && \
    npm audit fix --force || true

# Copy source code
COPY uiengine/src/ ./src/
COPY uiengine/tsconfig.json ./

# Build application
RUN npm run build

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Create necessary directories with proper permissions
RUN mkdir -p /app/logs /app/data && \
    chown -R nodejs:nodejs /app && \
    chmod -R 755 /app

# Switch to non-root user
USER nodejs

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/api/health || exit 1

EXPOSE 3001
CMD ["npm", "start"]
