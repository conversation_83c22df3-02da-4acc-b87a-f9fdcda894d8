import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Form, Spinner, <PERSON><PERSON>, Badge } from 'react-bootstrap';
import { Filter, Trash2, Plus } from 'lucide-react';
import {
  discoverBoundaryLayers,
  getLayerSchema,
  loadRegions,
  BoundaryLayer,
  LayerSchema,
  Region
} from '../../services/boundaryService';

// Use imported types from boundaryService

interface SelectedRegion {
  id: string;
  name: string;
  hierarchy: Record<string, string>;
  geometry: any;
}

interface RegionalFilterModalProps {
  show: boolean;
  onHide: () => void;
  onApplySelection: (regions: SelectedRegion[], boundaryLayer: string) => void;
}

const RegionalFilterModal: React.FC<RegionalFilterModalProps> = ({
  show,
  onHide,
  onApplySelection
}) => {
  // Loading states
  const [isDiscoveringLayers, setIsDiscoveringLayers] = useState(false);
  const [isLoadingSchema, setIsLoadingSchema] = useState(false);
  const [isLoadingRegions, setIsLoadingRegions] = useState(false);
  
  // Data states
  const [boundaryLayers, setBoundaryLayers] = useState<BoundaryLayer[]>([]);
  const [selectedLayer, setSelectedLayer] = useState<string>('');
  const [layerSchema, setLayerSchema] = useState<LayerSchema | null>(null);
  const [availableRegions, setAvailableRegions] = useState<Record<string, Region[]>>({});
  const [selectedRegions, setSelectedRegions] = useState<SelectedRegion[]>([]);
  
  // UI states
  const [error, setError] = useState<string | null>(null);
  const [currentHierarchyLevel, setCurrentHierarchyLevel] = useState(0);
  const [hierarchySelections, setHierarchySelections] = useState<Record<number, string>>({});

  // Discover boundary layers when modal opens
  useEffect(() => {
    if (show && boundaryLayers.length === 0) {
      discoverBoundaryLayersFunc();
    }
  }, [show]);

  const discoverBoundaryLayersFunc = async () => {
    setIsDiscoveringLayers(true);
    setError(null);

    try {
      console.log('Starting boundary layer discovery...');
      const layers = await discoverBoundaryLayers();

      setBoundaryLayers(layers);

      // Auto-select first layer if available
      if (layers.length > 0) {
        setSelectedLayer(layers[0].name);
        await loadLayerSchemaFunc(layers[0].name);
      } else {
        setError('No boundary layers found in the WFS service.');
      }

    } catch (error) {
      console.error('Failed to discover boundary layers:', error);
      setError('Failed to discover boundary layers. Please check your connection and try again.');
    } finally {
      setIsDiscoveringLayers(false);
    }
  };

  const loadLayerSchemaFunc = async (layerName: string) => {
    setIsLoadingSchema(true);
    setError(null);

    try {
      console.log(`Loading schema for layer: ${layerName}`);
      const schema = await getLayerSchema(layerName);

      setLayerSchema(schema);

      // Load initial regions for the first hierarchy level
      if (schema.hierarchyFields.length > 0) {
        await loadRegionsForLevelFunc(layerName, 0);
      }

    } catch (error) {
      console.error('Failed to load layer schema:', error);
      setError('Failed to load layer information. Please try again.');
    } finally {
      setIsLoadingSchema(false);
    }
  };

  const loadRegionsForLevelFunc = async (layerName: string, level: number, parentValue?: string) => {
    setIsLoadingRegions(true);
    setError(null);

    try {
      console.log(`Loading regions for level ${level}, parent: ${parentValue}`);

      // Get parent field for filtering
      let parentField: string | undefined;
      if (layerSchema && level > 0 && parentValue) {
        parentField = layerSchema.hierarchyFields[level - 1]?.field;
      }

      const regions = await loadRegions(layerName, level, parentField, parentValue);

      setAvailableRegions(prev => ({
        ...prev,
        [`${layerName}-${level}-${parentValue || 'root'}`]: regions
      }));

    } catch (error) {
      console.error('Failed to load regions:', error);
      setError('Failed to load regions. Please try again.');
    } finally {
      setIsLoadingRegions(false);
    }
  };

  const handleLayerChange = async (layerName: string) => {
    setSelectedLayer(layerName);
    setLayerSchema(null);
    setAvailableRegions({});
    setHierarchySelections({});
    setCurrentHierarchyLevel(0);

    await loadLayerSchemaFunc(layerName);
  };

  const handleHierarchySelection = async (level: number, value: string) => {
    setHierarchySelections(prev => ({
      ...prev,
      [level]: value
    }));

    // Load next level if available
    if (layerSchema && level + 1 < layerSchema.hierarchyFields.length) {
      await loadRegionsForLevelFunc(selectedLayer, level + 1, value);
    }
  };

  const handleAddRegion = () => {
    if (!layerSchema) return;

    const currentLevel = Math.max(...Object.keys(hierarchySelections).map(Number));
    const regionKey = `${selectedLayer}-${currentLevel}-${hierarchySelections[currentLevel - 1] || 'root'}`;
    const regions = availableRegions[regionKey] || [];
    const selectedRegionId = hierarchySelections[currentLevel];
    const region = regions.find(r => r.name === selectedRegionId);

    if (region) {
      const newRegion: SelectedRegion = {
        id: region.id,
        name: region.name,
        hierarchy: region.hierarchy,
        geometry: region.geometry || {}
      };

      // Check if region is already selected
      if (!selectedRegions.find(r => r.id === newRegion.id)) {
        setSelectedRegions(prev => [...prev, newRegion]);
      }
    }
  };

  const handleRemoveRegion = (regionId: string) => {
    setSelectedRegions(prev => prev.filter(r => r.id !== regionId));
  };

  const handleApply = () => {
    if (selectedRegions.length > 0 && selectedLayer) {
      onApplySelection(selectedRegions, selectedLayer);
      onHide();
    }
  };

  const handleCancel = () => {
    // Reset state
    setSelectedRegions([]);
    setHierarchySelections({});
    setCurrentHierarchyLevel(0);
    onHide();
  };

  const currentLayer = boundaryLayers.find(l => l.name === selectedLayer);

  return (
    <Modal show={show} onHide={handleCancel} size="lg" centered>
      <Modal.Header style={{ backgroundColor: '#007bff', color: 'white' }}>
        <Modal.Title>
          Define Area by Administrative Boundaries
        </Modal.Title>
      </Modal.Header>
      
      <Modal.Body>
        {/* Step 1: Boundary Layer Selection */}
        <div className="mb-4">
          <h6 className="text-primary mb-3">Step 1: Select Boundary Layer</h6>
          
          {isDiscoveringLayers ? (
            <div className="text-center py-3">
              <Spinner animation="border" size="sm" className="me-2" />
              <span className="text-muted">Discovering boundary layers...</span>
            </div>
          ) : boundaryLayers.length > 0 ? (
            <Form.Select
              value={selectedLayer}
              onChange={(e) => handleLayerChange(e.target.value)}
              disabled={isLoadingSchema}
            >
              {boundaryLayers.map(layer => (
                <option key={layer.name} value={layer.name}>
                  {layer.title}
                </option>
              ))}
            </Form.Select>
          ) : (
            <Alert variant="warning">
              No boundary layers found. Please check your GeoServer configuration.
            </Alert>
          )}
        </div>

        {/* Step 2: Region Selection */}
        {currentLayer && (
          <div className="mb-4">
            <h6 className="text-primary mb-3">Step 2: Select Regions</h6>
            
            {isLoadingSchema ? (
              <div className="text-center py-3">
                <Spinner animation="border" size="sm" className="me-2" />
                <span className="text-muted">Loading layer information...</span>
              </div>
            ) : layerSchema ? (
              <div>
                {layerSchema.hierarchyFields.map((field, index) => {
                  const regionKey = `${selectedLayer}-${index}-${hierarchySelections[index - 1] || 'root'}`;
                  const regions = availableRegions[regionKey] || [];
                  const isDisabled = index > 0 && !hierarchySelections[index - 1];
                  
                  return (
                    <div key={field.field} className="mb-3">
                      <Form.Label>{field.displayName}:</Form.Label>
                      <Form.Select
                        value={hierarchySelections[index] || ''}
                        onChange={(e) => handleHierarchySelection(index, e.target.value)}
                        disabled={isDisabled || isLoadingRegions}
                      >
                        <option value="">Select {field.displayName}</option>
                        {regions.map(region => (
                          <option key={region.id} value={region.name}>
                            {region.name}
                          </option>
                        ))}
                      </Form.Select>
                    </div>
                  );
                })}
                
                {Object.keys(hierarchySelections).length > 0 && (
                  <Button
                    variant="outline-primary"
                    size="sm"
                    onClick={handleAddRegion}
                    className="d-flex align-items-center"
                    disabled={isLoadingRegions}
                  >
                    <Plus size={16} className="me-1" />
                    Add Region
                  </Button>
                )}
              </div>
            ) : (
              <Alert variant="info">
                Loading layer schema...
              </Alert>
            )}
          </div>
        )}

        {/* Step 3: Selected Regions */}
        {selectedRegions.length > 0 && (
          <div className="mb-4">
            <h6 className="text-primary mb-3">Step 3: Selected Regions</h6>
            <div className="border rounded p-3" style={{ maxHeight: '200px', overflowY: 'auto' }}>
              {selectedRegions.map(region => (
                <div key={region.id} className="d-flex align-items-center justify-content-between mb-2">
                  <div>
                    <Badge bg="secondary" className="me-2">
                      {Object.values(region.hierarchy).join(' - ')}
                    </Badge>
                  </div>
                  <Button
                    variant="outline-danger"
                    size="sm"
                    onClick={() => handleRemoveRegion(region.id)}
                  >
                    <Trash2 size={14} />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <Alert variant="danger" className="mb-3">
            {error}
          </Alert>
        )}
      </Modal.Body>
      
      <Modal.Footer>
        <Button variant="secondary" onClick={handleCancel}>
          Cancel
        </Button>
        <Button 
          variant="primary" 
          onClick={handleApply}
          disabled={selectedRegions.length === 0 || !selectedLayer}
        >
          Apply Selection ({selectedRegions.length} regions)
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default RegionalFilterModal;
