import { Request, Response } from 'express';
import { getFullDiscovery } from '../services/geoServerService';
import axios from 'axios';
import { parseStringPromise } from 'xml2js';
import https from 'https';

const GEOSERVER_BASE = process.env.GEOSERVER_URL;
const httpsAgent = new https.Agent({
  rejectUnauthorized: false
});

export const getCapabilities = async (req: Request, res: Response) => {
  try {
    const data = await getFullDiscovery();
    res.status(200).json(data);
  } catch (error) {
    console.error('Discovery error:', error);
    res.status(500).json({ error: 'Failed to retrieve layer capabilities' });
  }
};

export const getLayerMetadata = async (req: Request, res: Response) => {
  try {
    const { layerName } = req.params;

    if (!layerName) {
      return res.status(400).json({ error: 'Layer name is required' });
    }

    // Get DescribeLayer information
    const describeLayerUrl = `${GEOSERVER_BASE}/ows?SERVICE=WMS&REQUEST=DescribeLayer&LAYERS=${encodeURIComponent(layerName)}&VERSION=1.1.1`;

    let describeLayerData = null;
    try {
      const describeResponse = await axios.get(describeLayerUrl, { httpsAgent });
      const parsedDescribe = await parseStringPromise(describeResponse.data);

      const layerDescription = parsedDescribe['WMS_DescribeLayerResponse']?.['LayerDescription']?.[0];
      if (layerDescription) {
        describeLayerData = {
          name: layerDescription.$.name,
          wfs: layerDescription.$.wfs,
          owsURL: layerDescription.$.owsURL,
          owsType: layerDescription.$.owsType,
          typeName: layerDescription.Query?.[0]?.$.typeName
        };
      }
    } catch (error) {
      console.warn(`Could not retrieve DescribeLayer for ${layerName}:`, error);
    }

    // Get layer from full discovery for comprehensive metadata
    const fullDiscovery = await getFullDiscovery();
    const layerMetadata = fullDiscovery.find((layer: any) => layer.name === layerName);

    if (!layerMetadata) {
      return res.status(404).json({ error: 'Layer not found' });
    }

    // Combine layer metadata with describe layer information
    const combinedMetadata = {
      ...layerMetadata,
      describeLayer: describeLayerData
    };

    res.status(200).json(combinedMetadata);
  } catch (error) {
    console.error('Layer metadata error:', error);
    res.status(500).json({ error: 'Failed to retrieve layer metadata' });
  }
};


