import { LayerDiscovery } from '../types/discovery';
import { API_CONFIG } from '../config';

// Import dataset types
export interface CategoryInfo {
    key: string;
    title: string;
    description: string;
    count: number;
}

export interface GeoNodeDiscoveryResponse {
    success: boolean;
    total: number;
    layers: LayerDiscovery[];
    categories: CategoryInfo[];
}

export interface CategorizedLayers {
    [categoryKey: string]: LayerDiscovery[];
}

/**
 * Fetch layers from the new GeoNode discovery endpoint
 */
export async function fetchLayersFromGeoNode(): Promise<GeoNodeDiscoveryResponse> {
    try {

        
        const response = await fetch(`${API_CONFIG.BASE_URL}/datasets/geonode/discovery`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.message || 'Failed to fetch layers from GeoNode');
        }

        console.log(`Successfully fetched ${data.total} layers from GeoNode with ${data.categories.length} categories`);
        
        return data;
    } catch (error: any) {
        console.error('Error fetching layers from GeoNode:', error);
        throw new Error(`Failed to fetch layers: ${error.message}`);
    }
}

/**
 * Fetch categories from GeoNode
 */
export async function fetchCategoriesFromGeoNode(): Promise<CategoryInfo[]> {
    try {
        console.log('Fetching categories from GeoNode...');
        
        const response = await fetch(`${API_CONFIG.BASE_URL}/datasets/geonode/categories`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.message || 'Failed to fetch categories from GeoNode');
        }

        console.log(`Successfully fetched ${data.categories.length} categories from GeoNode`);
        
        return data.categories;
    } catch (error: any) {
        console.error('Error fetching categories from GeoNode:', error);
        throw new Error(`Failed to fetch categories: ${error.message}`);
    }
}

/**
 * Categorize layers based on their category property
 */
export function categorizeLayers(layers: LayerDiscovery[]): CategorizedLayers {
    const categorized: CategorizedLayers = {};
    
    layers.forEach(layer => {
        const categoryKey = layer.category || 'other';
        
        if (!categorized[categoryKey]) {
            categorized[categoryKey] = [];
        }
        
        categorized[categoryKey].push(layer);
    });
    
    console.log('Layers categorized:', Object.keys(categorized).map(key => 
        `${key}: ${categorized[key].length} layers`
    ));
    
    return categorized;
}

/**
 * Map category keys to user-friendly accordion section titles
 */
export function mapCategoryToAccordionSection(categoryKey: string): string {
    const categoryMapping: { [key: string]: string } = {
        // Common GeoNode categories
        'biota': 'Biological Data',
        'boundaries': 'Administrative Boundaries',
        'climatologyMeteorologyAtmosphere': 'Climate & Weather',
        'economy': 'Economic Data',
        'elevation': 'Elevation & Terrain',
        'environment': 'Environmental Data',
        'farming': 'Agriculture & Farming',
        'geoscientificInformation': 'Geological Data',
        'health': 'Health Data',
        'imageryBaseMapsEarthCover': 'Satellite & Imagery',
        'intelligenceMilitary': 'Military & Intelligence',
        'inlandWaters': 'Water Resources',
        'location': 'Location Services',
        'oceans': 'Marine & Coastal',
        'planningCadastre': 'Planning & Cadastre',
        'society': 'Social Data',
        'structure': 'Infrastructure',
        'transportation': 'Transportation',
        'utilitiesCommunication': 'Utilities & Communication',
        
        // Flood-specific categories
        'flood_risk': 'Flood Risk',
        'satellite': 'Satellite Data',
        'historical': 'Historical Data',
        'climatology': 'Climate Data',
        'settlements': 'Human Settlements',
        'service_points': 'Service Points',
        'admin': 'Administrative Layers',
        'basemaps': 'Basemaps & Elevation',
        'other': 'Other Datasets'
    };
    
    return categoryMapping[categoryKey] || formatCategoryTitle(categoryKey);
}

/**
 * Format category key into a readable title
 */
function formatCategoryTitle(categoryKey: string): string {
    return categoryKey
        .split(/[_-]/)
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
}

/**
 * Create accordion sections from categories
 */
export function createAccordionSections(categories: CategoryInfo[]): Array<{key: string, title: string, content: null}> {
    return categories.map(category => ({
        key: category.key,
        title: category.title || mapCategoryToAccordionSection(category.key),
        content: null
    }));
}

/**
 * Enhanced layer discovery that combines GeoNode data with existing functionality
 */
export async function discoverLayers(): Promise<{
    layers: LayerDiscovery[];
    categories: CategoryInfo[];
    categorized: CategorizedLayers;
    accordionSections: Array<{key: string, title: string, content: null}>;
}> {
    try {
        console.log('Starting enhanced layer discovery...');
        
        // Fetch from GeoNode
        const geoNodeData = await fetchLayersFromGeoNode();

        // Debug remote layer detection
        const remoteLayers = geoNodeData.layers.filter(l => (l as any).isRemote);
        const localLayers = geoNodeData.layers.filter(l => !(l as any).isRemote);

        console.log(`🔍 Discovery: Found ${geoNodeData.layers.length} total layers`);
        console.log(`🏠 Discovery: Local layers: ${localLayers.length}`);
        console.log(`🌐 Discovery: Remote layers: ${remoteLayers.length}`);

        if (remoteLayers.length > 0) {
          console.log('🌐 Discovery: Remote layer details:', remoteLayers.map(l => ({
            name: l.name,
            title: l.title,
            serviceType: (l as any).serviceType,
            remoteUrl: (l as any).remoteUrl
          })));
        }

        // Categorize the layers
        const categorized = categorizeLayers(geoNodeData.layers);

        // Create accordion sections
        const accordionSections = createAccordionSections(geoNodeData.categories);

        console.log('Enhanced layer discovery completed successfully');
        
        return {
            layers: geoNodeData.layers,
            categories: geoNodeData.categories,
            categorized,
            accordionSections
        };
        
    } catch (error: any) {
        console.error('Enhanced layer discovery failed:', error);
        
        // Fallback to empty data structure
        return {
            layers: [],
            categories: [],
            categorized: {},
            accordionSections: [
                { key: 'satellite', title: 'Satellite Data', content: null },
                { key: 'flood_risk', title: 'Flood Risk', content: null },
                { key: 'historical', title: 'Historical Data', content: null },
                { key: 'climatology', title: 'Climate Data', content: null },
                { key: 'settlements', title: 'Human Settlements', content: null },
                { key: 'admin', title: 'Administrative Layers', content: null },
                { key: 'service_points', title: 'Service Points', content: null },
                { key: 'other', title: 'Other Datasets', content: null },
                { key: 'basemaps', title: 'Basemaps & Elevation', content: null }
            ]
        };
    }
}

/**
 * Legacy compatibility function - maps to the new discovery system
 */
export async function fetchAvailableLayers(): Promise<LayerDiscovery[]> {
    try {
        const discovery = await discoverLayers();
        return discovery.layers;
    } catch (error) {
        console.error('Legacy fetchAvailableLayers failed:', error);
        return [];
    }
}
