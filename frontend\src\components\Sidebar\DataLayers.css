/* Layer Options Dropdown Styling */
.layer-options-toggle {
  transition: all 0.2s ease;
}

.layer-options-toggle:hover {
  background-color: #5a6268 !important;
  transform: scale(1.1);
}

.layer-options-menu {
  min-width: 160px;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 4px 0;
}

.layer-options-menu .dropdown-item {
  padding: 8px 12px;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
}

.layer-options-menu .dropdown-item:hover {
  background-color: #f8f9fa;
  color: #495057;
  transform: translateX(2px);
}

.layer-options-menu .dropdown-item:active {
  background-color: #e9ecef;
}

/* Ensure dropdown doesn't interfere with layer selection */
.layer-label-with-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

/* Custom dropdown toggle to prevent Bootstrap default styling */
.layer-options-toggle::after {
  display: none !important;
}

.layer-options-toggle:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Animation for dropdown appearance */
.layer-options-menu {
  animation: dropdownFadeIn 0.15s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .layer-options-menu {
    min-width: 140px;
  }
  
  .layer-options-menu .dropdown-item {
    padding: 6px 10px;
    font-size: 0.75rem;
  }
}
