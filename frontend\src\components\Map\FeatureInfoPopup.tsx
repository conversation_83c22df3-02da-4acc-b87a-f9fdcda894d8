import React from 'react';
import { <PERSON>, Badge, Button } from 'react-bootstrap';
import { X, Info, MapPin } from 'lucide-react';
import './FeatureInfoPopup.css';

export interface FeatureInfo {
  layerName: string;
  features: Array<{
    id?: string;
    properties: Record<string, any>;
    geometry?: any;
  }>;
  coordinates: {
    lat: number;
    lng: number;
  };
}

interface FeatureInfoPopupProps {
  featureInfo: FeatureInfo | null;
  onClose: () => void;
  position: {
    x: number;
    y: number;
  };
}

const FeatureInfoPopup: React.FC<FeatureInfoPopupProps> = ({
  featureInfo,
  onClose,
  position
}) => {
  if (!featureInfo || !featureInfo.features.length) {
    return null;
  }

  const formatValue = (key: string, value: any): string => {
    if (value === null || value === undefined) return 'N/A';
    if (typeof value === 'boolean') return value ? 'Yes' : 'No';
    if (typeof value === 'number') {
      // Format numbers with appropriate precision
      if (Number.isInteger(value)) return value.toString();
      return value.toFixed(2);
    }
    if (typeof value === 'string') {
      // Handle date strings
      if (key.toLowerCase().includes('date') || key.toLowerCase().includes('time')) {
        try {
          const date = new Date(value);
          if (!isNaN(date.getTime())) {
            return date.toLocaleDateString();
          }
        } catch (e) {
          // Fall through to return original string
        }
      }
      return value;
    }
    return String(value);
  };

  const renderFeatureProperties = (properties: Record<string, any>) => {
    const entries = Object.entries(properties).filter(([key, value]) => 
      value !== null && value !== undefined && value !== ''
    );

    if (entries.length === 0) {
      return <p className="text-muted">No properties available</p>;
    }

    return (
      <div className="feature-properties">
        {entries.map(([key, value]) => (
          <div key={key} className="property-row">
            <strong className="property-key">{key}:</strong>
            <span className="property-value">{formatValue(key, value)}</span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div 
      className="feature-info-popup"
      style={{
        position: 'absolute',
        left: position.x,
        top: position.y,
        zIndex: 1000
      }}
    >
      <Card className="shadow-lg">
        <Card.Header className="d-flex justify-content-between align-items-center py-2 app-header-blue">
          <div className="d-flex align-items-center">
            <Info size={16} className="me-2 header-icon" />
            <span className="fw-bold">Feature Information</span>
          </div>
          <Button
            variant="outline-light"
            size="sm"
            onClick={onClose}
            className="p-1"
          >
            <X size={14} />
          </Button>
        </Card.Header>
        
        <Card.Body className="p-3">
          <div className="mb-2">
            <Badge bg="secondary" className="me-2">
              {featureInfo.layerName}
            </Badge>
            <small className="text-muted d-flex align-items-center">
              <MapPin size={12} className="me-1" />
              {featureInfo.coordinates.lat.toFixed(4)}, {featureInfo.coordinates.lng.toFixed(4)}
            </small>
          </div>

          {featureInfo.features.length === 1 ? (
            // Single feature
            <div>
              {featureInfo.features[0].id && (
                <div className="mb-2">
                  <small className="text-muted">Feature ID: {featureInfo.features[0].id}</small>
                </div>
              )}
              {renderFeatureProperties(featureInfo.features[0].properties)}
            </div>
          ) : (
            // Multiple features
            <div>
              <p className="mb-2">
                <Badge bg="info">{featureInfo.features.length} features found</Badge>
              </p>
              {featureInfo.features.map((feature, index) => (
                <div key={index} className="mb-3">
                  <h6 className="mb-2">
                    Feature {index + 1}
                    {feature.id && <small className="text-muted ms-2">ID: {feature.id}</small>}
                  </h6>
                  {renderFeatureProperties(feature.properties)}
                  {index < featureInfo.features.length - 1 && <hr className="my-2" />}
                </div>
              ))}
            </div>
          )}
        </Card.Body>
      </Card>
    </div>
  );
};

export default FeatureInfoPopup;
