import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { LayerGroup, Rectangle, Circle, Polygon, WMSTileLayer } from 'react-leaflet';
import { fetchFloodRiskData, fetchLayerData } from '../../services/mapService';
import { fetchAvailableWMSLayers, WMSLayer } from '../../services/geoserverService';
import LoadingOverlay from './LoadingOverlay';

interface MapLayersProps {
  selectedLayers: {
    sentinel: boolean;
    floodRisk: boolean;
    cbers: boolean;
    cadastre: boolean;
    dwsVillage: boolean;
    nasaPower: boolean;
    eumetsat: boolean;
    streamflow: boolean;
    historicalFlood: boolean;
    soilMoisture: boolean;
  };
  dateRange: {
    startDate: string;
    endDate: string;
  };
  bounds: any;
}

const MapLayers: React.FC<MapLayersProps> = ({ selectedLayers, dateRange, bounds }) => {
  // Only keep state for layers that are selected
  const [floodRiskAreas, setFloodRiskAreas] = useState<any[]>([]);
  const [cadastreData, setCadastreData] = useState<any[]>([]);
  const [dwsVillages, setDwsVillages] = useState<any[]>([]);
  const [wmsLayers, setWmsLayers] = useState<WMSLayer[]>([]);
  const [loadingLayers, setLoadingLayers] = useState<{ [key: string]: boolean }>({});
  const [errorLayers, setErrorLayers] = useState<{ [key: string]: string }>({});
  const [layerProgress, setLayerProgress] = useState<{[key: string]: number}>({});
  const [tileLoadingState, setTileLoadingState] = useState<{[key: string]: { loaded: number; total: number }}>({});

  const getLayerBounds = useCallback((bbox?: WMSLayer['bbox']) => {
    if (!bbox) return undefined;
    const minx = Number(bbox.minx), miny = Number(bbox.miny), maxx = Number(bbox.maxx), maxy = Number(bbox.maxy);
    if ([minx, miny, maxx, maxy].some(isNaN)) return undefined;
    // Leaflet expects [ [southWestLat, southWestLng], [northEastLat, northEastLng] ]
    return [
      [miny, minx],
      [maxy, maxx]
    ] as [[number, number], [number, number]];
  }, []);

  // Fetch WMS layers only if any WMS-based layer is selected
  useEffect(() => {
    // Check if any WMS layer is selected
    const anyWMSLayerSelected = (
      selectedLayers.sentinel ||
      selectedLayers.floodRisk ||
      selectedLayers.cbers ||
      selectedLayers.cadastre ||
      selectedLayers.dwsVillage ||
      selectedLayers.nasaPower ||
      selectedLayers.eumetsat ||
      selectedLayers.streamflow ||
      selectedLayers.historicalFlood ||
      selectedLayers.soilMoisture
    );

    if (anyWMSLayerSelected) {
      console.log('🗺️ Loading layers...');
      fetchAvailableWMSLayers().then(setWmsLayers).catch((error: any) => {
        console.error('Failed to load WMS layers:', error);
        setWmsLayers([]);
      });
    } else {
      setWmsLayers([]);
    }
  }, [selectedLayers]);

  useEffect(() => {
    if (selectedLayers.floodRisk) {
      fetchFloodRiskData(bounds, dateRange)
        .then(setFloodRiskAreas)
        .catch(console.error);
    } else {
      setFloodRiskAreas([]);
    }
  }, [selectedLayers.floodRisk, dateRange, bounds]);

  useEffect(() => {
    if (selectedLayers.cadastre) {
      fetchLayerData('cadastre', bounds)
        .then(setCadastreData)
        .catch(console.error);
    } else {
      setCadastreData([]);
    }
  }, [selectedLayers.cadastre, bounds]);

  useEffect(() => {
    if (selectedLayers.dwsVillage) {
      fetchLayerData('dwsVillage', bounds)
        .then(setDwsVillages)
        .catch(console.error);
    } else {
      setDwsVillages([]);
    }
  }, [selectedLayers.dwsVillage, bounds]);

  const getRiskColor = useCallback((risk: string) => {
    switch (risk) {
      case 'high':
        return 'red';
      case 'moderate':
        return 'yellow';
      case 'low':
        return 'green';
      default:
        return 'blue';
    }
  }, []);

  // Real progress tracking - tile loading event handlers
  const handleTileLoadStart = useCallback((layerName: string) => {
    setTileLoadingState(prevState => {
      const current = prevState[layerName] || { loaded: 0, total: 0 };
      return {
        ...prevState,
        [layerName]: { ...current, total: current.total + 1 }
      };
    });
  }, []);

  const handleTileLoad = useCallback((layerName: string) => {
    setTileLoadingState(prevState => {
      const current = prevState[layerName] || { loaded: 0, total: 0 };
      const newState = {
        ...prevState,
        [layerName]: { ...current, loaded: current.loaded + 1 }
      };
      
      // Calculate and update progress
      if (current.total > 0) {
        const newLoaded = current.loaded + 1;
        const progress = Math.round((newLoaded / current.total) * 100);
        console.log(`Tile loading progress for ${layerName}:`, {
          loaded: newLoaded,
          total: current.total,
          progress: `${progress}%`,
          currentState: tileLoadingState[layerName]
        });
        setLayerProgress(prevProgress => ({
          ...prevProgress,
          [layerName]: Math.min(progress, 100)
        }));
      }
      
      return newState;
    });
  }, [tileLoadingState]);

  const handleLayerLoading = useCallback((layerName: string, isLoading: boolean) => {
    setLoadingLayers(prev => ({ ...prev, [layerName]: isLoading }));
    if (isLoading) {
      // Reset progress when starting to load
      console.log(`Starting to load layer ${layerName}, resetting tile state:`, tileLoadingState[layerName]);
      setLayerProgress(prev => ({ ...prev, [layerName]: 0 }));
      setTileLoadingState(prev => ({ ...prev, [layerName]: { loaded: 0, total: 0 } }));
    }
  }, [tileLoadingState]);

  const handleRetryLayer = useCallback(async (layerName: string) => {
    console.log(`🔄 MapLayers - Retrying layer: ${layerName}`);
    
    // Clear error state
    setErrorLayers(prev => {
      const newErrors = { ...prev };
      delete newErrors[layerName];
      return newErrors;
    });
    
    // Reset loading state
    setLoadingLayers(prev => ({ ...prev, [layerName]: true }));
    setLayerProgress(prev => ({ ...prev, [layerName]: 0 }));
    setTileLoadingState(prev => ({ ...prev, [layerName]: { loaded: 0, total: 0 } }));
    
    try {
      // Simulate retry delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log(`✅ MapLayers - Retry initiated for layer: ${layerName}`);
    } catch (error) {
      console.error(`❌ MapLayers - Retry failed for layer ${layerName}:`, error);
      setErrorLayers(prev => ({ 
        ...prev, 
        [layerName]: 'Retry failed - please try again'
      }));
      setLoadingLayers(prev => ({ ...prev, [layerName]: false }));
    }
  }, []);

  const handleCloseLoader = useCallback(() => {
    console.log('🔒 MapLayers - Closing loading overlay');
    setLoadingLayers({});
    setErrorLayers({});
    setLayerProgress({});
    setTileLoadingState({});
  }, []);

  // Memoize WMS layers to render only selected ones
  const visibleWmsLayers = useMemo(() =>
    wmsLayers.filter(layer => {
      const key = layer.name as keyof typeof selectedLayers;
      return selectedLayers[key];
    }),
    [wmsLayers, selectedLayers]
  );

  return (
    <>
      {/* Enhanced Loading Overlay */}
      <LoadingOverlay 
        loadingLayers={loadingLayers}
        wmsLayers={wmsLayers}
        errorLayers={errorLayers}
        layerProgress={layerProgress}
        onRetryLayer={handleRetryLayer}
        onCloseLoader={handleCloseLoader}
      />

      {/* Render WMS layers based on selection */}
      {visibleWmsLayers.map(layer => {
        // Dynamic format detection based on layer properties
        const format = layer.formats?.includes('image/jpeg') ? 'image/jpeg' : 'image/png';
        const transparent = format === 'image/png';
        const bounds = getLayerBounds(layer.bbox);
        
        // Add temporal parameters for soil moisture layer
        const params: any = {};
        if (isSoilMoisture && selectedLayers.soilMoisture && dateRange.startDate) {
          // Convert date format from YYYY/MM/DD to ISO format
          const startDate = dateRange.startDate.replace(/\//g, '-') + 'T00:00:00Z';
          const endDate = dateRange.endDate.replace(/\//g, '-') + 'T00:00:00Z';
          
          // Use time range if different dates, otherwise single time
          if (startDate !== endDate) {
            params.time = `${startDate}/${endDate}`;
          } else {
            params.time = startDate;
          }
        }
        
        return (
          <WMSTileLayer
            key={layer.name}
            url="/geoserver/wms"
            layers={layer.name}
            format={format}
            transparent={transparent}
            version="1.1.1"
            bounds={bounds}
            params={params}
            eventHandlers={{
              loading: () => handleLayerLoading(layer.name, true),
              load: () => {
                handleLayerLoading(layer.name, false);
                setErrorLayers(prev => {
                  const copy = { ...prev };
                  delete copy[layer.name];
                  return copy;
                });
              },
              error: () => setErrorLayers(prev => ({ ...prev, [layer.name]: 'Failed to load layer' })),
              tileloadstart: () => handleTileLoadStart(layer.name),
              tileload: () => handleTileLoad(layer.name),
              tileerror: () => setErrorLayers(prev => ({ ...prev, [layer.name]: 'Failed to load tiles' }))
            }}
          />
        );
      })}

      {selectedLayers.floodRisk && (
        <LayerGroup>
          {floodRiskAreas.map((area, index) => (
            <Polygon
              key={`flood-risk-${area.id || index}`}
              positions={area.coordinates}
              pathOptions={{
                color: getRiskColor(area.risk),
                fillColor: getRiskColor(area.risk),
                fillOpacity: 0.4,
              }}
            />
          ))}
        </LayerGroup>
      )}

      {selectedLayers.cadastre && (
        <LayerGroup>
          {cadastreData.map((cadastre, index) => (
            <Rectangle
              key={`cadastre-${cadastre.id || index}`}
              bounds={cadastre.bounds}
              pathOptions={{
                color: 'purple',
                weight: 1,
                fillOpacity: 0.2,
              }}
            />
          ))}
        </LayerGroup>
      )}

      {selectedLayers.dwsVillage && (
        <LayerGroup>
          {dwsVillages.map((village, index) => (
            <Circle
              key={`village-${village.id || index}`}
              center={village.center}
              radius={village.radius}
              pathOptions={{
                color: 'blue',
                fillColor: 'blue',
                fillOpacity: 0.3,
              }}
            />
          ))}
        </LayerGroup>
      )}
    </>
  );
};

export default MapLayers;



