import React, { useState, useEffect } from 'react';
import { Modal, Button, Form, Row, Col, Collapse } from 'react-bootstrap';
import { Calendar, Clock, ChevronRight, ChevronDown, ChevronUp } from 'lucide-react';

interface TemporalSelectionModalProps {
  show: boolean;
  onHide: () => void;
  onConfirm: (dateRange: { startDate: string; endDate: string }) => void;
  aoiCoordinates?: any;
  preSelectedDateRange?: { startDate: string; endDate: string };
}

const TemporalSelectionModal: React.FC<TemporalSelectionModalProps> = ({
  show,
  onHide,
  onConfirm,
  aoiCoordinates,
  preSelectedDateRange
}) => {
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [useSlider, setUseSlider] = useState(false);
  const [showQuickSelect, setShowQuickSelect] = useState(false);
  const [showCustomRange, setShowCustomRange] = useState(false);

  // Pre-populate dates when modal opens with pre-selected date range
  useEffect(() => {
    if (show && preSelectedDateRange) {
      // Convert from MM/DD/YYYY to YYYY-MM-DD format for date inputs
      const formatForInput = (dateStr: string) => {
        const parts = dateStr.split('/');
        if (parts.length === 3) {
          return `${parts[2]}-${parts[0].padStart(2, '0')}-${parts[1].padStart(2, '0')}`;
        }
        return dateStr;
      };

      setStartDate(formatForInput(preSelectedDateRange.startDate));
      setEndDate(formatForInput(preSelectedDateRange.endDate));
    } else if (show && !preSelectedDateRange) {
      // Clear dates if no pre-selected range
      setStartDate('');
      setEndDate('');
    }
  }, [show, preSelectedDateRange]);

  // Generate suggested date ranges
  const generateSuggestedRanges = () => {
    const now = new Date();
    const currentYear = now.getFullYear();
    
    return [
      {
        label: 'Last 30 Days',
        start: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
        end: now
      },
      {
        label: 'Current Year',
        start: new Date(currentYear, 0, 1),
        end: now
      },
      {
        label: 'Flood Season (Oct-Mar)',
        start: new Date(currentYear - 1, 9, 1), // October previous year
        end: new Date(currentYear, 2, 31) // March current year
      },
      {
        label: 'Dry Season (Apr-Sep)',
        start: new Date(currentYear, 3, 1), // April
        end: new Date(currentYear, 8, 30) // September
      }
    ];
  };

  const formatDateForInput = (date: Date) => {
    return date.toISOString().split('T')[0];
  };

  const handleSuggestedRange = (start: Date, end: Date) => {
    setStartDate(formatDateForInput(start));
    setEndDate(formatDateForInput(end));
  };

  const handleConfirm = () => {
    if (startDate && endDate) {
      onConfirm({
        startDate: startDate.split('-').join('/'),
        endDate: endDate.split('-').join('/')
      });
    }
  };

  const suggestedRanges = generateSuggestedRanges();

  return (
    <Modal 
      show={show} 
      onHide={onHide} 
      size="lg" 
      centered
      backdrop="static"
    >
      <Modal.Header 
        closeButton 
        style={{ 
          backgroundColor: 'var(--bs-primary)', 
          color: 'white',
          border: 'none'
        }}
      >
        <Modal.Title style={{ color: 'white' }}>
          <Clock size={20} className="me-2" />
          Select Temporal Range for Area of Interest
        </Modal.Title>
      </Modal.Header>
      
      <Modal.Body className="p-4">
        <div className="mb-4">
          <h6 className="text-muted mb-3">
            <Calendar size={16} className="me-2" />
            Choose a time period for your area analysis
          </h6>

          {/* Pre-selected Date Range Indicator */}
          {preSelectedDateRange && (
            <div className="mb-3 p-3 bg-info bg-opacity-10 border border-info rounded">
              <small className="text-info fw-bold">
                ✓ Using previously selected date range: {new Date(preSelectedDateRange.startDate.split('/').join('-')).toLocaleDateString()} - {new Date(preSelectedDateRange.endDate.split('/').join('-')).toLocaleDateString()}
              </small>
            </div>
          )}
          
          {/* Collapsible Quick Selection */}
          <div className="mb-3">
            <Button
              variant="outline-secondary"
              size="sm"
              onClick={() => setShowQuickSelect(!showQuickSelect)}
              className="w-100 d-flex align-items-center justify-content-between"
              style={{ fontSize: '0.85rem' }}
            >
              <span>Quick Selection</span>
              {showQuickSelect ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </Button>
            <Collapse in={showQuickSelect}>
              <div className="mt-2">
                <div className="d-flex flex-wrap gap-2">
                  {suggestedRanges.map((range, index) => (
                    <Button
                      key={index}
                      variant="outline-primary"
                      size="sm"
                      onClick={() => {
                        handleSuggestedRange(range.start, range.end);
                        setShowQuickSelect(false);
                      }}
                      className="text-nowrap"
                      style={{ fontSize: '0.75rem' }}
                    >
                      {range.label}
                    </Button>
                  ))}
                </div>
              </div>
            </Collapse>
          </div>

          {/* Collapsible Custom Date Selection */}
          <div className="mb-4">
            <Button
              variant="outline-secondary"
              size="sm"
              onClick={() => setShowCustomRange(!showCustomRange)}
              className="w-100 d-flex align-items-center justify-content-between"
              style={{ fontSize: '0.85rem' }}
            >
              <span>Custom Date Range</span>
              {showCustomRange ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </Button>
            <Collapse in={showCustomRange}>
              <div className="border rounded p-3 bg-light mt-2">
            <Row>
              <Col md={6}>
                <Form.Group>
                  <Form.Label>Start Date:</Form.Label>
                  <Form.Control
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    max={formatDateForInput(new Date())}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group>
                  <Form.Label>End Date:</Form.Label>
                  <Form.Control
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    min={startDate}
                    max={formatDateForInput(new Date())}
                  />
                </Form.Group>
              </Col>
                </Row>
              </div>
            </Collapse>
          </div>

          {/* Date Range Summary */}
          {startDate && endDate && (
            <div className="mt-3 p-3 bg-info bg-opacity-10 border border-info rounded">
              <small className="text-info fw-bold">
                Selected Range: {new Date(startDate).toLocaleDateString()} - {new Date(endDate).toLocaleDateString()}
                <br />
                Duration: {Math.ceil((new Date(endDate).getTime() - new Date(startDate).getTime()) / (1000 * 60 * 60 * 24))} days
              </small>
            </div>
          )}
        </div>
      </Modal.Body>
      
      <Modal.Footer className="d-flex justify-content-between">
        <Button variant="secondary" onClick={onHide}>
          Cancel
        </Button>
        <Button 
          variant="primary" 
          onClick={handleConfirm}
          disabled={!startDate || !endDate}
          className="d-flex align-items-center"
        >
          Continue to Preview
          <ChevronRight size={16} className="ms-1" />
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default TemporalSelectionModal;
