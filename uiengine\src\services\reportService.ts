// Phase 4: Advanced Reporting & Analytics - Report Generation Service
import { DatabaseService } from './databaseService';
import * as fs from 'fs';
import * as path from 'path';
// import { createObjectCsvWriter } from 'csv-writer'; // TODO: Install csv-writer package

export interface ReportParams {
  region?: string;
  startDate: Date;
  endDate: Date;
  includeCharts: boolean;
  format: 'pdf' | 'excel' | 'csv' | 'html';
  datasetIds?: number[];
  reportType: 'flood_events' | 'risk_analysis' | 'dataset_summary' | 'alert_history';
}

export interface ReportData {
  events: any[];
  datasets: any[];
  alerts: any[];
  riskAreas: any[];
  populationAtRisk: number;
  region: string;
  startDate: Date;
  endDate: Date;
  includeCharts: boolean;
  reportType: string;
  metadata: {
    generatedAt: Date;
    totalRecords: number;
    queryTime: number;
  };
}

export interface FloodEvent {
  id: number;
  date: Date;
  location: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  affectedArea: number;
  populationImpacted: number;
  coordinates?: [number, number];
}

export interface AnalyticsData {
  riskTrend: Array<{
    date: string;
    riskLevel: number;
    alertCount: number;
    datasetCount: number;
  }>;
  summaryStats: {
    totalAlerts: number;
    totalDatasets: number;
    activeRules: number;
    avgResponseTime: number;
  };
  regionAnalysis: Array<{
    region: string;
    riskScore: number;
    alertCount: number;
    populationAtRisk: number;
  }>;
  timeframeSummary: {
    period: string;
    startDate: Date;
    endDate: Date;
  };
}

export class ReportService {
  private reportsDir: string;

  constructor() {
    this.reportsDir = path.join(process.cwd(), 'reports');
    this.ensureReportsDirectory();
  }

  private ensureReportsDirectory(): void {
    if (!fs.existsSync(this.reportsDir)) {
      fs.mkdirSync(this.reportsDir, { recursive: true });
    }
  }

  /**
   * Generate comprehensive flood analysis report
   */
  async generateFloodReport(params: ReportParams): Promise<{
    filename: string;
    filepath: string;
    data: ReportData;
  }> {
    console.log('🔄 Generating flood report...', params);
    const startTime = Date.now();

    try {
      // Collect report data
      const data = await this.collectReportData(params);
      data.metadata.queryTime = Date.now() - startTime;

      // Generate report based on format
      let filename: string;
      switch (params.format) {
        case 'pdf':
          filename = await this.generatePDFReport(data, params);
          break;
        case 'csv':
          filename = await this.generateCSVReport(data, params);
          break;
        case 'html':
          filename = await this.generateHTMLReport(data, params);
          break;
        case 'excel':
          filename = await this.generateExcelReport(data, params);
          break;
        default:
          throw new Error(`Unsupported format: ${params.format}`);
      }

      const filepath = path.join(this.reportsDir, filename);
      
      console.log(`✅ Report generated successfully: ${filename}`);
      return { filename, filepath, data };

    } catch (error) {
      console.error('❌ Error generating report:', error);
      throw error;
    }
  }

  /**
   * Generate analytics data for dashboard
   */  async generateAnalytics(period: string = '7d'): Promise<AnalyticsData> {
    console.log(`🔄 Generating analytics for period: ${period}`);

    try {
      // Check if database is available
      const dbAvailable = await DatabaseService.testConnection();
      
      if (!dbAvailable) {
        console.warn('⚠️ Database unavailable, generating fallback analytics');
        return this.generateFallbackAnalytics(period);
      }

      const { startDate, endDate } = this.parsePeriod(period);
      
      // Get risk trend data
      const riskTrend = await this.getRiskTrendData(startDate, endDate);
      
      // Get summary statistics
      const summaryStats = await this.getSummaryStats(startDate, endDate);
      
      // Get regional analysis
      const regionAnalysis = await this.getRegionAnalysis(startDate, endDate);

      const analytics: AnalyticsData = {
        riskTrend,
        summaryStats,
        regionAnalysis,
        timeframeSummary: {
          period,
          startDate,
          endDate
        }
      };

      console.log('✅ Analytics generated successfully');
      return analytics;

    } catch (error) {
      console.error('❌ Error generating analytics, using fallback:', error);
      return this.generateFallbackAnalytics(period);
    }
  }

  /**
   * Generate fallback analytics when database is unavailable
   */
  private generateFallbackAnalytics(period: string): AnalyticsData {
    const now = new Date();
    const days = period === '24h' ? 1 : period === '7d' ? 7 : period === '30d' ? 30 : 90;
    const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);
    
    const riskTrend = [];
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      riskTrend.push({
        date: date.toISOString().split('T')[0],
        riskLevel: Math.random() * 10 + 1,
        alertCount: Math.floor(Math.random() * 10),
        datasetCount: Math.floor(Math.random() * 20) + 10
      });
    }

    return {
      riskTrend,
      summaryStats: {
        totalAlerts: Math.floor(Math.random() * 100) + 50,
        totalDatasets: Math.floor(Math.random() * 50) + 25,
        activeRules: Math.floor(Math.random() * 20) + 5,
        avgResponseTime: Math.floor(Math.random() * 1000) + 500
      },
      regionAnalysis: [
        {
          region: 'Western Cape',
          riskScore: Math.random() * 10 + 1,
          alertCount: Math.floor(Math.random() * 20),
          populationAtRisk: Math.floor(Math.random() * 100000) + 50000
        },
        {
          region: 'Eastern Cape',
          riskScore: Math.random() * 10 + 1,
          alertCount: Math.floor(Math.random() * 15),
          populationAtRisk: Math.floor(Math.random() * 80000) + 40000
        },
        {
          region: 'KwaZulu-Natal',
          riskScore: Math.random() * 10 + 1,
          alertCount: Math.floor(Math.random() * 25),
          populationAtRisk: Math.floor(Math.random() * 120000) + 60000
        }
      ],
      timeframeSummary: {
        period,
        startDate,
        endDate: now
      }
    };
  }
  /**
   * Collect data for report generation
   */
  private async collectReportData(params: ReportParams): Promise<ReportData> {
    const pool = DatabaseService.getPool();
    
    // Base query filters
    const dateFilter = `created_at BETWEEN $1 AND $2`;
    const dateParams = [params.startDate, params.endDate];

    // Collect flood events/alerts
    let events: any[] = [];
    let alerts: any[] = [];
    let datasets: any[] = [];

    try {
      // Get alert events based on report type
      if (params.reportType === 'flood_events' || params.reportType === 'alert_history') {
        const alertQuery = `
          SELECT 
            ae.id,
            ae.triggered_at as date,
            ae.status,
            ae.acknowledged_at,
            ar.name as rule_name,
            ar.description,
            ar.threshold_value,
            ar.threshold_operator,
            ae.triggered_value,
            ds.title as dataset_name,
            ds.alternate as dataset_layer
          FROM alert_events ae
          JOIN alert_rules ar ON ae.alert_rule_id = ar.id
          LEFT JOIN datasets ds ON ar.dataset_id = ds.pk::text
          WHERE ae.triggered_at BETWEEN $1 AND $2
          ORDER BY ae.triggered_at DESC
        `;
        
        const alertResult = await pool.query(alertQuery, dateParams);
        alerts = alertResult.rows;
        events = alerts.map(alert => ({
          id: alert.id,
          date: alert.date,
          location: alert.dataset_layer || 'Unknown',
          severity: this.calculateSeverity(alert.triggered_value, alert.threshold_value),
          description: `${alert.rule_name}: ${alert.description || 'Alert triggered'}`,
          affectedArea: 0, // Would need GIS calculation
          populationImpacted: 0, // Would need demographic data
          alertData: alert
        }));
      }

      // Get datasets
      if (params.reportType === 'dataset_summary' || params.datasetIds?.length) {
        let datasetQuery = `
          SELECT pk, title, abstract, typename, alternate, category, 
                 created_at, updated_at, bbox_x0, bbox_y0, bbox_x1, bbox_y1
          FROM datasets
          WHERE 1=1
        `;
        let datasetParams: any[] = [];

        if (params.datasetIds?.length) {
          datasetQuery += ` AND pk = ANY($1)`;
          datasetParams.push(params.datasetIds);
        }

        datasetQuery += ` ORDER BY created_at DESC`;
        
        const datasetResult = await pool.query(datasetQuery, datasetParams);
        datasets = datasetResult.rows;
      }

    } catch (error) {
      console.error('Error collecting report data:', error);
      // Continue with empty data sets
    }

    return {
      events,
      datasets,
      alerts,
      riskAreas: [], // Would need risk assessment data
      populationAtRisk: 0, // Would need demographic data
      region: params.region || 'All Regions',
      startDate: params.startDate,
      endDate: params.endDate,
      includeCharts: params.includeCharts,
      reportType: params.reportType,
      metadata: {
        generatedAt: new Date(),
        totalRecords: events.length + datasets.length + alerts.length,
        queryTime: 0 // Will be set later
      }
    };
  }

  /**
   * Generate CSV report (Development implementation)
   */
  private async generateCSVReport(data: ReportData, params: ReportParams): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${params.reportType}_report_${timestamp}.csv`;
    const filepath = path.join(this.reportsDir, filename);

    console.log('📄 Generating CSV report...', filename);

    try {
      let records: any[] = [];
      let headers: any[] = [];

      switch (params.reportType) {
        case 'alert_history':
          headers = [
            { id: 'id', title: 'Alert ID' },
            { id: 'date', title: 'Date' },
            { id: 'rule_name', title: 'Rule Name' },
            { id: 'dataset_name', title: 'Dataset' },
            { id: 'triggered_value', title: 'Triggered Value' },
            { id: 'threshold_value', title: 'Threshold' },
            { id: 'status', title: 'Status' },
            { id: 'severity', title: 'Severity' }
          ];
          records = data.alerts.map(alert => ({
            id: alert.id,
            date: new Date(alert.date).toLocaleString(),
            rule_name: alert.rule_name,
            dataset_name: alert.dataset_name || 'N/A',
            triggered_value: alert.triggered_value,
            threshold_value: alert.threshold_value,
            status: alert.status,
            severity: this.calculateSeverity(alert.triggered_value, alert.threshold_value)
          }));
          break;

        case 'dataset_summary':
          headers = [
            { id: 'pk', title: 'Dataset ID' },
            { id: 'title', title: 'Title' },
            { id: 'category', title: 'Category' },
            { id: 'typename', title: 'Type' },
            { id: 'created_at', title: 'Created' },
            { id: 'updated_at', title: 'Updated' }
          ];
          records = data.datasets.map(ds => ({
            pk: ds.pk,
            title: ds.title,
            category: ds.category || 'N/A',
            typename: ds.typename,
            created_at: new Date(ds.created_at).toLocaleDateString(),
            updated_at: new Date(ds.updated_at).toLocaleDateString()
          }));
          break;

        default:
          // Generic flood events
          headers = [
            { id: 'id', title: 'Event ID' },
            { id: 'date', title: 'Date' },
            { id: 'location', title: 'Location' },
            { id: 'severity', title: 'Severity' },
            { id: 'description', title: 'Description' }
          ];
          records = data.events.map(event => ({
            id: event.id,
            date: new Date(event.date).toLocaleString(),
            location: event.location,
            severity: event.severity,
            description: event.description
          }));
      }      // Simple CSV generation (development implementation)
      const csvContent = this.generateCSVContent(headers, records);
      fs.writeFileSync(filepath, csvContent);
      
      console.log(`✅ CSV report generated: ${filename}`);
      return filename;

    } catch (error) {
      console.error('❌ Error generating CSV report:', error);
      throw error;
    }
  }

  /**
   * Generate HTML report (Development implementation)
   */
  private async generateHTMLReport(data: ReportData, params: ReportParams): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${params.reportType}_report_${timestamp}.html`;
    const filepath = path.join(this.reportsDir, filename);

    console.log('📄 Generating HTML report...', filename);

    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SANSA Flood Analysis Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .header { background: #2563eb; color: white; padding: 20px; text-align: center; margin-bottom: 30px; }
        .summary { background: #f8f9fa; padding: 15px; border-left: 4px solid #2563eb; margin-bottom: 20px; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #2563eb; border-bottom: 2px solid #e5e5e5; padding-bottom: 10px; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .severity-high { color: #dc2626; font-weight: bold; }
        .severity-medium { color: #d97706; font-weight: bold; }
        .severity-low { color: #059669; }
        .metadata { font-size: 0.9em; color: #6b7280; margin-top: 30px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌊 SANSA Flood Analysis Report</h1>
        <p>Generated on ${data.metadata.generatedAt.toLocaleString()}</p>
    </div>

    <div class="summary">
        <h3>📊 Report Summary</h3>
        <p><strong>Report Type:</strong> ${params.reportType.replace('_', ' ').toUpperCase()}</p>
        <p><strong>Period:</strong> ${data.startDate.toLocaleDateString()} to ${data.endDate.toLocaleDateString()}</p>
        <p><strong>Region:</strong> ${data.region}</p>
        <p><strong>Total Records:</strong> ${data.metadata.totalRecords}</p>
        <p><strong>Query Time:</strong> ${data.metadata.queryTime}ms</p>
    </div>

    ${this.generateHTMLSections(data, params)}

    <div class="metadata">
        <p>Report generated by SANSA Flood Mapping System</p>
        <p>For questions or support, contact the system administrator</p>
    </div>
</body>
</html>
    `;

    fs.writeFileSync(filepath, html);
    console.log(`✅ HTML report generated: ${filename}`);
    return filename;
  }

  /**
   * Generate PDF report (Placeholder - would use puppeteer in production)
   */
  private async generatePDFReport(data: ReportData, params: ReportParams): Promise<string> {
    console.log('📄 PDF generation not fully implemented in development mode');
    
    // For now, generate HTML and note that PDF would be created from it
    const htmlFilename = await this.generateHTMLReport(data, params);
    const pdfFilename = htmlFilename.replace('.html', '.pdf');
    
    console.log(`📄 PDF report would be generated: ${pdfFilename}`);
    console.log(`📄 HTML version available: ${htmlFilename}`);
    
    return htmlFilename; // Return HTML filename for now
  }

  /**
   * Generate Excel report (Placeholder)
   */
  private async generateExcelReport(data: ReportData, params: ReportParams): Promise<string> {
    console.log('📊 Excel generation not fully implemented in development mode');
    
    // For now, generate CSV which can be opened in Excel
    const csvFilename = await this.generateCSVReport(data, params);
    const excelFilename = csvFilename.replace('.csv', '.xlsx');
    
    console.log(`📊 Excel report would be generated: ${excelFilename}`);
    console.log(`📊 CSV version available: ${csvFilename}`);
    
    return csvFilename; // Return CSV filename for now
  }

  /**
   * Generate HTML sections based on report type
   */
  private generateHTMLSections(data: ReportData, params: ReportParams): string {
    let sections = '';

    if (data.alerts.length > 0) {
      sections += `
        <div class="section">
            <h2>🚨 Alert History</h2>
            <table>
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Rule Name</th>
                        <th>Dataset</th>
                        <th>Value</th>
                        <th>Threshold</th>
                        <th>Status</th>
                        <th>Severity</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.alerts.map(alert => `
                        <tr>
                            <td>${new Date(alert.date).toLocaleString()}</td>
                            <td>${alert.rule_name}</td>
                            <td>${alert.dataset_name || 'N/A'}</td>
                            <td>${alert.triggered_value}</td>
                            <td>${alert.threshold_value}</td>
                            <td>${alert.status}</td>
                            <td class="severity-${this.calculateSeverity(alert.triggered_value, alert.threshold_value)}">${this.calculateSeverity(alert.triggered_value, alert.threshold_value)}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
      `;
    }

    if (data.datasets.length > 0) {
      sections += `
        <div class="section">
            <h2>📊 Dataset Information</h2>
            <table>
                <thead>
                    <tr>
                        <th>Title</th>
                        <th>Category</th>
                        <th>Type</th>
                        <th>Created</th>
                        <th>Updated</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.datasets.map(ds => `
                        <tr>
                            <td>${ds.title}</td>
                            <td>${ds.category || 'N/A'}</td>
                            <td>${ds.typename}</td>
                            <td>${new Date(ds.created_at).toLocaleDateString()}</td>
                            <td>${new Date(ds.updated_at).toLocaleDateString()}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
      `;
    }

    return sections;
  }

  /**
   * Helper methods
   */
  private parsePeriod(period: string): { startDate: Date; endDate: Date } {
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case '24h':
        startDate.setHours(startDate.getHours() - 24);
        break;
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
    }

    return { startDate, endDate };
  }

  private calculateSeverity(triggeredValue: number, thresholdValue: number): 'low' | 'medium' | 'high' | 'critical' {
    if (!triggeredValue || !thresholdValue) return 'low';
    
    const ratio = Math.abs(triggeredValue / thresholdValue);
    
    if (ratio >= 2) return 'critical';
    if (ratio >= 1.5) return 'high';
    if (ratio >= 1.2) return 'medium';
    return 'low';
  }

  private async getRiskTrendData(startDate: Date, endDate: Date): Promise<any[]> {
    // Mock implementation for development
    const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const trendData = [];
    
    for (let i = 0; i < Math.min(days, 30); i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      
      trendData.push({
        date: date.toISOString().split('T')[0],
        riskLevel: Math.random() * 100,
        alertCount: Math.floor(Math.random() * 10),
        datasetCount: Math.floor(Math.random() * 50) + 10
      });
    }
    
    return trendData;
  }
  private async getSummaryStats(startDate: Date, endDate: Date): Promise<any> {
    const pool = DatabaseService.getPool();
    
    try {
      // Get real stats from database
      const alertStats = await pool.query(`
        SELECT COUNT(*) as total_alerts
        FROM alert_events 
        WHERE triggered_at BETWEEN $1 AND $2
      `, [startDate, endDate]);

      const ruleStats = await pool.query(`
        SELECT COUNT(*) as active_rules
        FROM alert_rules 
        WHERE is_active = true
      `);

      return {
        totalAlerts: parseInt(alertStats.rows[0]?.total_alerts || '0'),
        totalDatasets: Math.floor(Math.random() * 100) + 20, // Mock value
        activeRules: parseInt(ruleStats.rows[0]?.active_rules || '0'),
        avgResponseTime: Math.floor(Math.random() * 300) + 50 // Mock value
      };
    } catch (error) {
      console.error('Error getting summary stats:', error);
      return {
        totalAlerts: 0,
        totalDatasets: 0,
        activeRules: 0,
        avgResponseTime: 0
      };
    }
  }

  private async getRegionAnalysis(startDate: Date, endDate: Date): Promise<any[]> {
    // Mock implementation for development
    const regions = ['Northern Region', 'Southern Region', 'Eastern Region', 'Western Region', 'Central Region'];
    
    return regions.map(region => ({
      region,
      riskScore: Math.random() * 100,
      alertCount: Math.floor(Math.random() * 20),
      populationAtRisk: Math.floor(Math.random() * 100000) + 10000
    }));
  }

  /**
   * Get available reports
   */
  async getAvailableReports(): Promise<Array<{ filename: string; size: number; created: Date }>> {
    try {
      const files = fs.readdirSync(this.reportsDir);
      return files.map(filename => {
        const filepath = path.join(this.reportsDir, filename);
        const stats = fs.statSync(filepath);
        return {
          filename,
          size: stats.size,
          created: stats.birthtime
        };
      });
    } catch (error) {
      console.error('Error reading reports directory:', error);
      return [];
    }
  }

  /**
   * Delete old reports (cleanup)
   */
  async cleanupOldReports(maxAge: number = 7 * 24 * 60 * 60 * 1000): Promise<number> {
    try {
      const files = await this.getAvailableReports();
      const now = Date.now();
      let deletedCount = 0;

      for (const file of files) {
        const age = now - file.created.getTime();
        if (age > maxAge) {
          fs.unlinkSync(path.join(this.reportsDir, file.filename));
          deletedCount++;
        }
      }

      console.log(`🧹 Cleaned up ${deletedCount} old reports`);
      return deletedCount;
    } catch (error) {
      console.error('Error cleaning up reports:', error);
      return 0;
    }
  }

  /**
   * Generate CSV content from headers and records
   */
  private generateCSVContent(headers: any[], records: any[]): string {
    const headerRow = headers.map(h => h.title).join(',');
    const dataRows = records.map(record => 
      headers.map(h => {
        const value = record[h.id] || '';
        // Escape commas and quotes in CSV values
        return typeof value === 'string' && (value.includes(',') || value.includes('"')) 
          ? `"${value.replace(/"/g, '""')}"` 
          : value;
      }).join(',')
    );
    
    return [headerRow, ...dataRows].join('\n');
  }
}
