import swaggerJSDoc from 'swagger-jsdoc';

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'SANSA UI Engine API',
      version: '1.0.0',
      description: 'API documentation for the SANSA UI engine',
    },
    servers: [
      {
        url: 'http://localhost:3001',
        description: 'Development server'
      },
    ],
  },
  apis: ['./src/routes/*.ts', './src/models/*.ts'], // adjust path to your routes/models
};

const swaggerSpec = swaggerJSDoc(options);

export default swaggerSpec;