import { useState, useEffect } from 'react';
import { Form } from 'react-bootstrap';
import RegionSelector from './RegionSelector';
import DataLayers from './DataLayers';
import DataActions from './DataActions';
import ServiceDetails from './ServiceDetails';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import './Sidebar.css';
import { LayerDiscovery } from '../../types/discovery';
import {
  loadProvinces,
  loadMunicipalities,
  loadDistricts,
  loadWards,
  AdministrativeRegion
} from '../../services/administrativeBoundaryService';

interface SidebarProps {
  layers: LayerDiscovery[];
  selectedLayerNames: string[];
  onLayerChange: (layerName: string) => void;
  dateRange: {
    startDate: string;
    endDate: string;
  };
  onDateChange: (type: 'startDate' | 'endDate', value: string) => void;
  onSearch: (query: string) => void;
  onPreviewData: () => void;
  onDownloadData: () => void;
  onQueryTemporalData?: () => void;
  isLoading?: boolean;
  error?: string | null;
  // AOI functionality
  onDrawModeToggle: (isDrawing: boolean) => void;
  isDrawingMode: boolean;
  hasDrawnArea: boolean;
  onClearDrawnArea: () => void;
  // Regional AOI functionality
  aoiMethod: 'drawn' | 'regional';
  onAOIMethodChange: (method: 'drawn' | 'regional') => void;
  hasRegionalSelection: boolean;
  onConfigureRegions: () => void;
  onClearRegionalSelection: () => void;
  // Predefined polygon functionality
  onPredefinedPolygon: (size: string) => void;
  // Basemap functionality
  selectedBasemap?: string;
  onBasemapChange?: (basemapName: string) => void;
  // Coordinate functionality
  onCoordinatePinModeToggle?: (enabled: boolean) => void;
  currentCoordinates?: string;
}

function Sidebar(props: SidebarProps) {
  // State for sidebar collapse
  const [isCollapsed, setIsCollapsed] = useState(false);

  // State for region of interest toggle
  const [selectRegionOfInterest, setSelectRegionOfInterest] = useState(false);

  // State for nested section toggles
  const [nestedSections, setNestedSections] = useState({
    administrative: true,  // Expanded by default
    coordinates: false,    // Collapsed by default
    dateRange: false,      // Collapsed by default
    drawingTools: false    // Collapsed by default
  });

  // State for coordinate input
  const [coordinates, setCoordinates] = useState('');
  const [isPinningMode, setIsPinningMode] = useState(false);

  // Local type definitions for districts and wards
  type DistrictInfo = {
    id: string;
    name: string;
    code: string;
    isMetro?: boolean;
  };

  type WardInfo = {
    id: string;
    name: string;
    code?: string;
  };

  // State for administrative boundaries
  const [administrativeBoundaries, setAdministrativeBoundaries] = useState({
    provinces: [] as AdministrativeRegion[],
    municipalities: [] as AdministrativeRegion[],
    districts: [] as DistrictInfo[],
    wards: [] as WardInfo[]
  });

  // State for selected administrative regions (with codes)
  const [selectedRegions, setSelectedRegions] = useState({
    province: '', // province id
    provinceName: '', // province name
    municipality: '',
    municipalityCode: '',
    district: '',
    districtCode: '',
    ward: '',
    wardCode: ''
  });

  // Loading states for administrative boundaries
  const [boundaryLoading, setBoundaryLoading] = useState({
    provinces: false,
    municipalities: false,
    districts: false,
    wards: false
  });

  const toggleNestedSection = (section: keyof typeof nestedSections) => {
    const newState = !nestedSections[section];
    
    setNestedSections(prev => ({
      ...prev,
      [section]: newState
    }));
    
    // If toggling coordinates section, handle pin mode
    if (section === 'coordinates') {
      // When closing coordinates section, also disable pin mode
      if (!newState && isPinningMode) {
        setIsPinningMode(false);
        if (props.onCoordinatePinModeToggle) {
          props.onCoordinatePinModeToggle(false);
        }
      }
    }
  };

  // Use props directly instead of destructuring

  // Load provinces on component mount
  useEffect(() => {
    const loadProvincesData = async () => {
      setBoundaryLoading(prev => ({ ...prev, provinces: true }));
      try {
        const provinces = await loadProvinces();
        setAdministrativeBoundaries(prev => ({ ...prev, provinces }));
      } catch (error) {
        console.error('Failed to load provinces:', error);
      } finally {
        setBoundaryLoading(prev => ({ ...prev, provinces: false }));
      }
    };

    loadProvincesData();
  }, []);

  // Load municipalities when province changes
  useEffect(() => {
    if (selectedRegions.province) {
      const loadMunicipalitiesData = async () => {
        setBoundaryLoading(prev => ({ ...prev, municipalities: true }));
        try {
          const municipalities = await loadMunicipalities(selectedRegions.province);
          setAdministrativeBoundaries(prev => ({ ...prev, municipalities }));
          // Clear dependent selections
          setSelectedRegions(prev => ({
            ...prev,
            municipality: '',
            municipalityCode: '',
            district: '',
            districtCode: '',
            ward: '',
            wardCode: ''
          }));
        } catch (error) {
          console.error('Failed to load municipalities:', error);
        } finally {
          setBoundaryLoading(prev => ({ ...prev, municipalities: false }));
        }
      };

      loadMunicipalitiesData();
    } else {
      // Clear municipalities if no province selected
      setAdministrativeBoundaries(prev => ({ ...prev, municipalities: [], districts: [], wards: [] }));
      setSelectedRegions(prev => ({
        ...prev,
        municipality: '',
        municipalityCode: '',
        district: '',
        districtCode: '',
        ward: '',
        wardCode: ''
      }));
    }
  }, [selectedRegions.province]);

  // Load districts when province changes (if available)
  useEffect(() => {
    if (selectedRegions.province) {
      const loadDistrictsData = async () => {
        setBoundaryLoading(prev => ({ ...prev, districts: true }));
        try {
          const districts = await loadDistricts(selectedRegions.province);
          setAdministrativeBoundaries(prev => ({ ...prev, districts }));
        } catch (error) {
          console.error('Failed to load districts:', error);
        } finally {
          setBoundaryLoading(prev => ({ ...prev, districts: false }));
        }
      };

      loadDistrictsData();
    }
  }, [selectedRegions.province]);
  
  // Load municipalities for district (handles metros and non-metros)
  useEffect(() => {
    const selectedDistrictInfo = administrativeBoundaries.districts.find(
      (d: DistrictInfo) => d.name === selectedRegions.district
    );
    console.log("Selected district for municipality loading:", selectedDistrictInfo);
    
    if (selectedRegions.district && selectedRegions.province) {
      const loadMunicipalitiesForDistrict = async () => {
        setBoundaryLoading(prev => ({ ...prev, municipalities: true }));
        try {
          if (selectedDistrictInfo?.isMetro) {
            // Metro areas skip to wards - no local municipalities
            console.log(`District ${selectedRegions.district} is a metropolitan municipality - skipping to wards`);
            
            // For metros, we need to set up the necessary data to load wards directly
            setAdministrativeBoundaries(prev => ({ ...prev, municipalities: [] }));
            
            // Even though the UI won't show municipalities for metros,
            // set the municipality code to the district code to help with ward loading
            setSelectedRegions(prev => ({
              ...prev,
              municipality: selectedRegions.district, // Use district name as municipality name for metros
              municipalityCode: selectedDistrictInfo.code, // Use district code as municipality code for metros
              ward: '',
              wardCode: ''
            }));
          } else {
            // Load local municipalities for this district
            console.log("Loading municipalities for district:", selectedRegions.district);
            const municipalities = await loadMunicipalities(selectedRegions.province, selectedRegions.district);
            console.log("Loaded municipalities:", municipalities);
            
            setAdministrativeBoundaries(prev => ({ ...prev, municipalities }));
            // Clear dependent selections
            setSelectedRegions(prev => ({
              ...prev,
              municipality: '',
              municipalityCode: '',
              ward: '',
              wardCode: ''
            }));
          }
        } catch (error) {
          console.error('Failed to load municipalities for district:', error);
        } finally {
          setBoundaryLoading(prev => ({ ...prev, municipalities: false }));
        }
      };

      loadMunicipalitiesForDistrict();
    }
  }, [selectedRegions.district, selectedRegions.province, administrativeBoundaries.districts]);

  // Load wards when municipality changes OR when district changes (for metros)
  useEffect(() => {
    // Check if we should load wards for a municipality or for a metro district
    const isMetroDistrict = selectedRegions.district && administrativeBoundaries.districts.find(
      d => d.name === selectedRegions.district && d.isMetro === true
    );
    
    const shouldLoadWards = selectedRegions.municipalityCode || isMetroDistrict;
    
    console.log("Should load wards:", shouldLoadWards);
    console.log("Selected municipality code:", selectedRegions.municipalityCode);
    console.log("Is metro district:", isMetroDistrict ? "Yes" : "No");

    if (shouldLoadWards) {
      const loadWardsData = async () => {
        setBoundaryLoading(prev => ({ ...prev, wards: true }));
        try {
          console.log("Loading wards with params:", {
            municipalityName: selectedRegions.municipality,
            municipalityCode: selectedRegions.municipalityCode,
            districtCode: selectedRegions.districtCode,
            provinceId: selectedRegions.province
          });
          
          let municipalityNameToUse = selectedRegions.municipality;
          let municipalityCodeToUse = selectedRegions.municipalityCode;
          
          // For metros, we might need to use the district code as the municipality code
          if (isMetroDistrict) {
            municipalityCodeToUse = selectedRegions.districtCode;
            municipalityNameToUse = selectedRegions.district;
            console.log("Using metro district as municipality:", municipalityNameToUse, municipalityCodeToUse);
          }
          
          const wards = await loadWards(
            municipalityNameToUse,
            municipalityCodeToUse,
            selectedRegions.districtCode,
            selectedRegions.province
          );
          
          console.log("Loaded wards:", wards);
          setAdministrativeBoundaries(prev => ({ ...prev, wards }));
          // Clear dependent selections
          setSelectedRegions(prev => ({ ...prev, ward: '', wardCode: '' }));
        } catch (error) {
          console.error('Failed to load wards:', error);
        } finally {
          setBoundaryLoading(prev => ({ ...prev, wards: false }));
        }
      };

      loadWardsData();
    } else {
      // Clear wards if no municipality or metro district selected
      setAdministrativeBoundaries(prev => ({ ...prev, wards: [] }));
      setSelectedRegions(prev => ({ ...prev, ward: '', wardCode: '' }));
    }
  }, [selectedRegions.municipality, selectedRegions.municipalityCode, selectedRegions.district, selectedRegions.districtCode, selectedRegions.province, administrativeBoundaries.districts]);

  // Handler for administrative region selection
  const handleRegionChange = (level: keyof typeof selectedRegions, value: string) => {
    console.log(`Changing ${level} to:`, value);
    const updates: any = { [level]: value };

    if (level === 'province') {
      // Find province name by id
      const selectedProvince = administrativeBoundaries.provinces.find(p => p.id === value);
      updates.provinceName = selectedProvince ? selectedProvince.name : '';
      // Clear all dependent selections
      updates.district = '';
      updates.districtCode = '';
      updates.municipality = '';
      updates.municipalityCode = '';
      updates.ward = '';
      updates.wardCode = '';
    }

    // When district changes, also store the district code
    if (level === 'district') {
      const selectedDistrictInfo = administrativeBoundaries.districts.find(
        d => d.name === value
      );
      updates.districtCode = selectedDistrictInfo?.code || '';
      console.log("Selected district info:", selectedDistrictInfo);
      
      // Clear dependent selections
      updates.municipality = '';
      updates.municipalityCode = '';
      updates.ward = '';
      updates.wardCode = '';
    }

    // When municipality changes, also store the municipality code
    if (level === 'municipality') {
      // The dropdown is now using the municipality code as value
      updates.municipalityCode = value; // Use the selected value directly as code
      
      // Also find and store the name for display purposes
      const selectedMunicipalityInfo = administrativeBoundaries.municipalities.find(
        m => m.code === value
      );
      console.log("Selected municipality:", selectedMunicipalityInfo);
      
      // Clear dependent selections
      updates.ward = '';
      updates.wardCode = '';
    }

    // When ward changes, also store the ward code
    if (level === 'ward') {
      updates.wardCode = value; // The dropdown now uses ward ID directly
      
      const selectedWardInfo = administrativeBoundaries.wards.find(
        w => w.id === value
      );
      console.log("Selected ward:", selectedWardInfo);
    }

    setSelectedRegions(prev => ({
      ...prev,
      ...updates
    }));
  };

  // Load sidebar state from localStorage
  useEffect(() => {
    const savedState = localStorage.getItem('sidebarCollapsed');
    if (savedState) {
      setIsCollapsed(savedState === 'true');
    }
  }, []);

  // Save sidebar state to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('sidebarCollapsed', isCollapsed.toString());
  }, [isCollapsed]);

  // Effect to update coordinates from props
  useEffect(() => {
    if (props.currentCoordinates) {
      setCoordinates(props.currentCoordinates);
    }
  }, [props.currentCoordinates]);

  // Effect to handle coordinate section state
  useEffect(() => {
    // If coordinates section is closed but pin mode is active, disable pin mode
    if (!nestedSections.coordinates && isPinningMode) {
      setIsPinningMode(false);
      if (props.onCoordinatePinModeToggle) {
        props.onCoordinatePinModeToggle(false);
      }
    }
  }, [nestedSections.coordinates, isPinningMode, props.onCoordinatePinModeToggle]);

  // Toggle sidebar collapse state
  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  // Let's also dispatch an event when the sidebar state changes
  useEffect(() => {
    window.dispatchEvent(
      new CustomEvent('sidebarToggle', {
        detail: { collapsed: isCollapsed }
      })
    );
  }, [isCollapsed]);

  // Check if any temporal layers are selected
  const hasTemporalLayers = props.selectedLayerNames.includes('soilMoisture');
  const getSelectedLayerName = () => (hasTemporalLayers ? 'Soil Moisture' : '');

  function onSearch(query: string): void {
    if (props.onSearch) {
      props.onSearch(query);
    }
  }

  function onDrawModeToggle(isDrawing: boolean): void {
    if (props.onDrawModeToggle) {
      props.onDrawModeToggle(isDrawing);
    }
  }

  function onClearDrawnArea(): void {
    if (props.onClearDrawnArea) {
      props.onClearDrawnArea();
    }
  }

  function onAOIMethodChange(method: 'drawn' | 'regional'): void {
    if (props.onAOIMethodChange) {
      props.onAOIMethodChange(method);
    }
  }

  function onConfigureRegions(): void {
    if (props.onConfigureRegions) {
      props.onConfigureRegions();
    }
  }

  function onClearRegionalSelection(): void {
    // Clear all selected administrative regions and boundaries
    setSelectedRegions({
      province: '',
      provinceName: '',
      municipality: '',
      municipalityCode: '',
      district: '',
      districtCode: '',
      ward: '',
      wardCode: ''
    });
    setAdministrativeBoundaries(prev => ({
      ...prev,
      municipalities: [],
      districts: [],
      wards: []
    }));
  }

  function onPredefinedPolygon(size: string): void {
    if (props.onPredefinedPolygon) {
      props.onPredefinedPolygon(size);
    }
  }

  function onPreviewData(): void {
    throw new Error('Function not implemented.');
  }

  function onDownloadData(): void {
    throw new Error('Function not implemented.');
  }

  function onQueryTemporalData(): void {
    throw new Error('Function not implemented.');
  }

  return (
    <div className={`sidebar ${isCollapsed ? 'collapsed' : ''}`}>
      <div className="sidebar-header" style={{ display: 'flex', alignItems: 'center', justifyContent: isCollapsed ? 'center' : 'space-between', height: 56 }}>
        {!isCollapsed && (
          <h1 className="app-title" style={{ margin: 0, flex: 1 }}>DATA ACCESS</h1>
        )}
        <button
          className="sidebar-toggle"
          onClick={toggleSidebar}
          aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          style={isCollapsed ? { margin: 0 } : { marginLeft: 8 }}
        >
          {isCollapsed ? <ChevronRight size={20} /> : <ChevronLeft size={20} />}
        </button>
      </div>
      {!isCollapsed && (
        <div className="sidebar-content">
          {/* Select Region of Interest Toggle */}
          <div className="sidebar-card">
            <div className="sidebar-card-body">
              <div className="d-flex align-items-center justify-content-between mb-3">
                <div className="d-flex align-items-center">
                  <span className="text-danger me-2">📍</span>
                  <span style={{ fontSize: '0.9rem', fontWeight: '500' }}>Select region of interest</span>
                </div>
                <Form.Check
                  type="switch"
                  id="region-toggle"
                  checked={selectRegionOfInterest}
                  onChange={(e) => setSelectRegionOfInterest(e.target.checked)}
                  className="custom-switch"
                />
              </div>
            </div>
          </div>

          {/* Region of Interest Card - Only show when toggle is ON */}
          {selectRegionOfInterest && (
            <div className="sidebar-card">
              <div className="sidebar-card-header">
                <h5 className="sidebar-card-title">Region of Interest</h5>
              </div>
              <div className="sidebar-card-body">
                {/* Administrative Boundaries Section */}
                <div className="nested-section mb-3">
                  <div
                    className="nested-section-header clickable"
                    onClick={() => toggleNestedSection('administrative')}
                  >
                    <div className="d-flex align-items-center">
                      <span className="nested-section-icon">🏛️</span>
                      <span className="nested-section-title">Administrative Boundaries</span>
                    </div>
                    <span className="nested-section-toggle">
                      {nestedSections.administrative ? '▼' : '▶'}
                    </span>
                  </div>
                  {nestedSections.administrative && (
                    <div className="nested-section-body">
                      <Form.Group className="mb-2">
                        <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white' }}>Province:</Form.Label>
                        <Form.Select
                          size="sm"
                          style={{
                            fontSize: '0.85rem',
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            border: '1px solid rgba(255, 255, 255, 0.3)',
                            color: '#333'
                          }}
                          value={selectedRegions.province}
                          onChange={(e) => handleRegionChange('province', e.target.value)}
                          disabled={boundaryLoading.provinces}
                        >
                          <option value="">
                            {boundaryLoading.provinces ? '-- Loading Provinces --' : '-- Select Province --'}
                          </option>
                          {administrativeBoundaries.provinces.map((province) => (
                            <option key={province.id} value={province.id}>
                              {province.name}
                            </option>
                          ))}
                        </Form.Select>
                        {/* Loader for districts below province dropdown */}
                        {selectedRegions.province && boundaryLoading.districts && (
                          <div style={{ fontSize: '0.8rem', color: '#ffc107', marginTop: 4 }}>Loading districts...</div>
                        )}
                      </Form.Group>

                      {/* Show District dropdown only if province is selected and districts loaded */}
                      {selectedRegions.province && !boundaryLoading.districts && administrativeBoundaries.districts.length > 0 && (
                        <Form.Group className="mb-2">
                          <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white' }}>District:</Form.Label>
                          <Form.Select
                            size="sm"
                            style={{
                              fontSize: '0.85rem',
                              backgroundColor: 'rgba(255, 255, 255, 0.95)',
                              border: '1px solid rgba(255, 255, 255, 0.3)',
                              color: '#333'
                            }}
                            value={selectedRegions.district}
                            onChange={(e) => handleRegionChange('district', e.target.value)}
                          >
                            <option value="">-- Select District --</option>
                            {administrativeBoundaries.districts.map((district) => (
                              <option key={district.id} value={district.name}>
                                {district.name} {district.isMetro ? '🟦 Metro' : '🟩 District'}
                              </option>
                            ))}
                          </Form.Select>
                          {/* Loader for municipalities below district dropdown */}
                          {selectedRegions.district && boundaryLoading.municipalities && (
                            <div style={{ fontSize: '0.8rem', color: '#ffc107', marginTop: 4 }}>Loading municipalities...</div>
                          )}
                        </Form.Group>
                      )}

                      {/* Show Municipality dropdown if district is selected and municipalities loaded */}
                      {selectedRegions.district && !boundaryLoading.municipalities && (
                        administrativeBoundaries.municipalities.length > 0 ? (
                          <Form.Group className="mb-2">
                            <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white' }}>Local Municipality:</Form.Label>
                            <Form.Select
                              size="sm"
                              style={{
                                fontSize: '0.85rem',
                                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                border: '1px solid rgba(255, 255, 255, 0.3)',
                                color: '#333'
                              }}
                              value={selectedRegions.municipalityCode}
                              onChange={(e) => handleRegionChange('municipality', e.target.value)}
                            >
                              <option value="">-- Select Municipality --</option>
                              {administrativeBoundaries.municipalities.map((municipality) => (
                                <option key={municipality.id} value={municipality.code}>
                                  {municipality.name}
                                </option>
                              ))}
                            </Form.Select>
                            {/* Loader for wards below municipality dropdown */}
                            {selectedRegions.municipalityCode && boundaryLoading.wards && (
                              <div style={{ fontSize: '0.8rem', color: '#ffc107', marginTop: 4 }}>Loading wards...</div>
                            )}
                          </Form.Group>
                        ) : (
                          // No municipalities found, but we have a district - show button to use district as AOI
                          <div className="mb-2">
                            <div style={{ fontSize: '0.85rem', color: '#ffc107', marginBottom: 8 }}>
                              No municipalities found for this district
                            </div>
                            <button 
                              className="btn btn-sm btn-warning" 
                              style={{ 
                                fontSize: '0.85rem', 
                                width: '100%',
                                backgroundColor: '#ffc107',
                                borderColor: '#e0a800'
                              }}
                              onClick={() => {
                                // Use the selected district as AOI
                                console.log("Using selected district as AOI:", selectedRegions.district);
                                props.onConfigureRegions();
                              }}
                            >
                              Use This District As Final Selection
                            </button>
                          </div>
                        )
                      )}

                      {/* Show Ward dropdown only if municipality is selected and wards loaded, or if metro skip municipality */}
                      {(selectedRegions.municipalityCode || 
                        (selectedRegions.district && administrativeBoundaries.districts.find(d => d.name === selectedRegions.district && d.isMetro))) && (
                        !boundaryLoading.wards && 
                        (administrativeBoundaries.wards.length > 0 ? (
                          <Form.Group className="mb-0">
                            <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white' }}>Ward:</Form.Label>
                            <Form.Select
                              size="sm"
                              style={{
                                fontSize: '0.85rem',
                                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                border: '1px solid rgba(255, 255, 255, 0.3)',
                                color: '#333'
                              }}
                              value={selectedRegions.wardCode}
                              onChange={(e) => handleRegionChange('ward', e.target.value)}
                            >
                              <option value="">-- Select Ward --</option>
                              {administrativeBoundaries.wards.map((ward) => (
                                <option key={ward.id} value={ward.id}>
                                  {ward.name}
                                </option>
                              ))}
                            </Form.Select>
                          </Form.Group>
                        ) : (
                          <div className="mb-0">
                            <div style={{ fontSize: '0.85rem', color: '#ffc107', marginBottom: 8 }}>
                              No wards found for this selection
                            </div>
                            <button 
                              className="btn btn-sm btn-warning" 
                              style={{ 
                                fontSize: '0.85rem', 
                                width: '100%',
                                backgroundColor: '#ffc107',
                                borderColor: '#e0a800'
                              }}
                              onClick={() => {
                                // Use the current selection (municipality or district) as the AOI
                                if (selectedRegions.municipalityCode) {
                                  console.log("Using selected municipality as AOI:", selectedRegions.municipality);
                                  props.onConfigureRegions(); // This should update the map with the selected boundaries
                                } else if (selectedRegions.district) {
                                  console.log("Using selected district as AOI:", selectedRegions.district);
                                  props.onConfigureRegions();
                                }
                              }}
                            >
                              Use Current Municipality As Final Selection
                            </button>
                          </div>
                        ))
                      )}
                    </div>
                  )}
                </div>

                {/* Coordinates Input Section */}
                <div className="nested-section mb-3">
                  <div
                    className="nested-section-header clickable"
                    onClick={() => toggleNestedSection('coordinates')}
                  >
                    <div className="d-flex align-items-center">
                      <span className="nested-section-icon">📍</span>
                      <span className="nested-section-title">Coordinate Entry</span>
                    </div>
                    <span className="nested-section-toggle">
                      {nestedSections.coordinates ? '▼' : '▶'}
                    </span>
                  </div>
                  {nestedSections.coordinates && (
                    <div className="nested-section-body">
                      <Form.Group className="mb-2">
                        <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white' }}>Enter coordinates:</Form.Label>
                        <div className="d-flex">
                          <Form.Control
                            type="text"
                            placeholder="latitude, longitude"
                            size="sm"
                            style={{ fontSize: '0.85rem' }}
                            value={props.currentCoordinates || ''}
                            readOnly={isPinningMode}
                          />
                          <button
                            className={`btn btn-sm ms-2 ${isPinningMode ? 'btn-danger' : 'btn-primary'}`}
                            onClick={() => {
                              setIsPinningMode(!isPinningMode);
                              // Toggle pin mode in parent component
                              if (props.onCoordinatePinModeToggle) {
                                props.onCoordinatePinModeToggle(!isPinningMode);
                              }
                            }}
                            title={isPinningMode ? 'Cancel pin placement' : 'Place pin on map'}
                          >
                            {isPinningMode ? '✕' : '📌'}
                          </button>
                        </div>
                        <small className={`d-block mt-1 ${isPinningMode ? 'text-warning' : 'text-muted'}`}>
                          {isPinningMode 
                            ? 'Click on map to place a pin at desired location' 
                            : 'Example: -26.2041, 28.0473 or click the pin button'}
                        </small>
                      </Form.Group>
                    </div>
                  )}
                </div>

                {/* Date Range Section */}
                <div className="nested-section mb-3">
                  <div
                    className="nested-section-header clickable"
                    onClick={() => toggleNestedSection('dateRange')}
                  >
                    <div className="d-flex align-items-center">
                      <span className="nested-section-icon">📅</span>
                      <span className="nested-section-title">Date Range</span>
                    </div>
                    <span className="nested-section-toggle">
                      {nestedSections.dateRange ? '▼' : '▶'}
                    </span>
                  </div>
                  {nestedSections.dateRange && (
                    <div className="nested-section-body">
                      <Form.Group className="mb-2">
                        <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white' }}>Start Date:</Form.Label>
                        <Form.Control
                          type="date"
                          value={props.dateRange.startDate.split('/').join('-')}
                          onChange={(e) => {
                            const date = e.target.value.split('-').join('/');
                            props.onDateChange('startDate', date);
                          }}
                          size="sm"
                          style={{ fontSize: '0.85rem' }}
                        />
                      </Form.Group>

                      <Form.Group className="mb-0">
                        <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white' }}>End Date:</Form.Label>
                        <Form.Control
                          type="date"
                          value={props.dateRange.endDate.split('/').join('-')}
                          onChange={(e) => {
                            const date = e.target.value.split('-').join('/');
                            props.onDateChange('endDate', date);
                          }}
                          size="sm"
                          style={{ fontSize: '0.85rem' }}
                        />
                      </Form.Group>
                    </div>
                  )}
                </div>

                {/* Drawing Tools Section */}
                <div className="nested-section mb-0">
                  <div
                    className="nested-section-header clickable"
                    onClick={() => toggleNestedSection('drawingTools')}
                  >
                    <div className="d-flex align-items-center">
                      <span className="nested-section-icon">✏️</span>
                      <span className="nested-section-title">Drawing Tools</span>
                    </div>
                    <span className="nested-section-toggle">
                      {nestedSections.drawingTools ? '▼' : '▶'}
                    </span>
                  </div>
                  {nestedSections.drawingTools && (
                    <div className="nested-section-body">
                      <RegionSelector
                        onSearch={onSearch}
                        onDrawModeToggle={onDrawModeToggle}
                        isDrawingMode={props.isDrawingMode}
                        hasDrawnArea={props.hasDrawnArea}
                        onClearDrawnArea={onClearDrawnArea}
                        aoiMethod={props.aoiMethod}
                        onAOIMethodChange={onAOIMethodChange}
                        hasRegionalSelection={props.hasRegionalSelection}
                        onConfigureRegions={onConfigureRegions}
                        onClearRegionalSelection={onClearRegionalSelection}
                        onPredefinedPolygon={onPredefinedPolygon}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Data Layers Card */}
          <div className="sidebar-card">
            <div className="sidebar-card-header">
              <h5 className="sidebar-card-title">Data Layers</h5>
            </div>
            <div className="sidebar-card-body">
              <DataLayers
                layers={props.layers}
                selectedLayerNames={props.selectedLayerNames}
                onLayerChange={props.onLayerChange}
                isLoading={props.isLoading}
                error={props.error}
                selectedBasemap={props.selectedBasemap}
                onBasemapChange={props.onBasemapChange}
              />
            </div>
          </div>

          {/* Service Details Card */}
          {/* <div className="sidebar-card">
            <div className="sidebar-card-header">
              <h5 className="sidebar-card-title">Layer Information</h5>
            </div>
            <div className="sidebar-card-body">
              <ServiceDetails
                selectedLayers={props.selectedLayerNames.reduce((acc, name) => {
                  acc[name] = true;
                  return acc;
                }, {} as Record<string, boolean>)}
              />
            </div>
          </div> */}

          {/* Actions Card */}
          <div className="sidebar-card">
            <div className="sidebar-card-header">
              <h5 className="sidebar-card-title">Actions</h5>
            </div>
            <div className="sidebar-card-body">
              <DataActions
                onPreviewData={onPreviewData}
                onDownloadData={onDownloadData}
                onQueryTemporalData={hasTemporalLayers ? onQueryTemporalData : undefined}
                temporalLayerName={getSelectedLayerName()}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Sidebar;