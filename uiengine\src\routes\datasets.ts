import { Router } from 'express';
import { datasetController } from '../controllers/datasetController';
import {
    fetchDatasets,
    categorizeDatasets,
    extractCategories,
    datasetToLayerDiscovery
} from '../services/datasetsService';
import { getFullDiscovery } from '../services/geoServerService';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Dataset:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique dataset identifier
 *         name:
 *           type: string
 *           description: Dataset name
 *         title:
 *           type: string
 *           description: Human-readable title
 *         description:
 *           type: string
 *           description: Dataset description
 *         keywords:
 *           type: array
 *           items:
 *             type: string
 *           description: Associated keywords
 *         temporal:
 *           type: boolean
 *           description: Whether dataset has temporal dimension
 *         queryable:
 *           type: boolean
 *           description: Whether dataset supports queries
 *         lastUpdated:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *         popularity:
 *           type: integer
 *           description: Usage popularity score
 */

// ===== NEW GEONODE DISCOVERY ROUTES =====

/**
 * @swagger
 * /api/datasets/geonode:
 *   get:
 *     summary: Fetch all datasets from GeoNode
 *     tags: [Datasets]
 *     description: Retrieves datasets from the GeoNode API and returns them with categorization
 *     responses:
 *       200:
 *         description: Successfully retrieved datasets
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 total:
 *                   type: number
 *                 datasets:
 *                   type: array
 *                   items:
 *                     type: object
 *                 categories:
 *                   type: array
 *                   items:
 *                     type: object
 *                 categorized:
 *                   type: object
 *       500:
 *         description: Failed to fetch datasets
 */
router.get('/geonode', async (req, res) => {
    try {
        console.log('API: Fetching datasets from GeoNode...');

        // Fetch datasets from GeoNode
        const datasetsResponse = await fetchDatasets();
        const datasets = datasetsResponse.datasets || [];

        // Extract categories
        const categories = extractCategories(datasets);

        // Categorize datasets
        const categorized = categorizeDatasets(datasets);

        console.log(`API: Successfully processed ${datasets.length} datasets into ${categories.length} categories`);

        res.json({
            success: true,
            total: datasets.length,
            page: datasetsResponse.page,
            page_size: datasetsResponse.page_size,
            datasets,
            categories,
            categorized
        });

    } catch (error: any) {
        console.error('API: Error fetching datasets:', error.message);
        res.status(500).json({
            success: false,
            error: error.message,
            message: 'Failed to fetch datasets from GeoNode'
        });
    }
});

/**
 * @swagger
 * /api/datasets/geonode/discovery:
 *   get:
 *     summary: Get datasets in LayerDiscovery format
 *     tags: [Datasets]
 *     description: Returns datasets converted to LayerDiscovery format for compatibility with existing frontend
 *     responses:
 *       200:
 *         description: Successfully retrieved datasets in LayerDiscovery format
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 layers:
 *                   type: array
 *                   items:
 *                     type: object
 *                 categories:
 *                   type: array
 *                   items:
 *                     type: object
 *       500:
 *         description: Failed to fetch datasets
 */
router.get('/geonode/discovery', async (req, res) => {
    try {
        console.log('API: Fetching datasets for discovery...');

        const datasetsResponse = await fetchDatasets();
        const datasets = datasetsResponse.datasets || [];

        // Convert datasets to LayerDiscovery format
        const allLayers = datasets.map(datasetToLayerDiscovery);

        // Separate local and remote layers
        const localLayers = allLayers.filter(layer => !layer.isRemote);
        const remoteLayers = allLayers.filter(layer => layer.isRemote);

        console.log(`API: Found ${localLayers.length} local layers and ${remoteLayers.length} remote layers`);

        // Validate only local layers against WMS capabilities
        console.log('API: Validating local layers against WMS capabilities...');
        let validationResults: any = {
            validated: false,
            localLayerCount: localLayers.length,
            remoteLayerCount: remoteLayers.length,
            totalCount: allLayers.length
        };

        try {
            const wmsLayers = await getFullDiscovery();
            const wmsLayerNames = new Set(wmsLayers.map(layer => layer.name));

            // Only validate local layers against WMS
            const matchingLocalLayers = localLayers.filter(layer => wmsLayerNames.has(layer.name));
            const missingLocalLayers = localLayers.filter(layer => !wmsLayerNames.has(layer.name));

            validationResults = {
                validated: true,
                localLayerCount: localLayers.length,
                remoteLayerCount: remoteLayers.length,
                totalCount: allLayers.length,
                matchingLocalLayers: matchingLocalLayers.length,
                missingLocalLayers: missingLocalLayers.length,
                matchingLayers: matchingLocalLayers.map(l => l.name),
                missingLayers: missingLocalLayers.map(l => l.name),
                remoteLayers: remoteLayers.map(l => ({ name: l.name, serviceType: l.serviceType, remoteUrl: l.remoteUrl })),
                wmsLayerCount: wmsLayers.length
            };

            console.log(`API: Local layer validation complete - ${matchingLocalLayers.length}/${localLayers.length} local layers match WMS capabilities`);
            console.log(`API: Remote layers (not validated against WMS): ${remoteLayers.length}`);

            if (missingLocalLayers.length > 0) {
                console.warn('API: Local layers missing from WMS capabilities:', missingLocalLayers.map(l => l.name));
            }

        } catch (wmsError) {
            console.warn('API: Could not validate against WMS capabilities:', wmsError);
        }

        // Extract categories from all datasets (including remote)
        const categories = extractCategories(datasets);

        console.log(`API: Converted ${datasets.length} datasets to LayerDiscovery format (${localLayers.length} local, ${remoteLayers.length} remote)`);

        res.json({
            success: true,
            total: allLayers.length,
            layers: allLayers, // Return both local and remote layers
            categories,
            validation: validationResults
        });

    } catch (error: any) {
        console.error('API: Error in discovery endpoint:', error.message);
        res.status(500).json({
            success: false,
            error: error.message,
            message: 'Failed to fetch datasets for discovery'
        });
    }
});

/**
 * @swagger
 * /api/datasets/geonode/categories:
 *   get:
 *     summary: Get dataset categories from GeoNode
 *     tags: [Datasets]
 *     description: Returns available dataset categories with counts
 *     responses:
 *       200:
 *         description: Successfully retrieved categories
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 categories:
 *                   type: array
 *                   items:
 *                     type: object
 *       500:
 *         description: Failed to fetch categories
 */
router.get('/geonode/categories', async (req, res) => {
    try {
        console.log('API: Fetching dataset categories...');

        const datasetsResponse = await fetchDatasets();
        const datasets = datasetsResponse.datasets || [];
        const categories = extractCategories(datasets);

        res.json({
            success: true,
            categories
        });

    } catch (error: any) {
        console.error('API: Error fetching categories:', error.message);
        res.status(500).json({
            success: false,
            error: error.message,
            message: 'Failed to fetch dataset categories'
        });
    }
});

/**
 * @swagger
 * /api/datasets/debug/layer-mapping:
 *   get:
 *     summary: Debug layer name mapping for GeoNode datasets
 *     tags: [Datasets]
 *     description: Shows how GeoNode dataset names are converted to WMS layer names
 *     responses:
 *       200:
 *         description: Layer mapping information
 */
router.get('/debug/layer-mapping', async (req, res) => {
    try {
        console.log('API: Debug layer mapping...');

        // Fetch GeoNode datasets
        const datasetsResponse = await fetchDatasets();
        const datasets = datasetsResponse.datasets || [];

        // Create mapping information
        const mapping = datasets.map(dataset => {
            const wmsLayerName = dataset.workspace && dataset.workspace !== 'geonode'
                ? `${dataset.workspace}:${dataset.name}`
                : dataset.name;

            return {
                geoNodeName: dataset.name,
                geoNodeTitle: dataset.title,
                workspace: dataset.workspace,
                wmsLayerName: wmsLayerName,
                category: dataset.category?.identifier
            };
        });

        res.json({
            success: true,
            summary: {
                totalGeoNodeDatasets: datasets.length,
                totalMappings: mapping.length
            },
            mappings: mapping.slice(0, 20), // Show first 20 for readability
            sampleMappings: mapping.slice(0, 5)
        });

    } catch (error: any) {
        console.error('API: Error in debug endpoint:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * @swagger
 * /api/datasets/validate/wms-matching:
 *   get:
 *     summary: Validate that GeoNode layer names exactly match WMS capabilities
 *     tags: [Datasets]
 *     description: Comprehensive validation of layer name matching between GeoNode and WMS
 *     responses:
 *       200:
 *         description: Validation results with detailed matching information
 */
router.get('/validate/wms-matching', async (req, res) => {
    try {
        console.log('API: Starting comprehensive WMS layer name validation...');

        // Fetch GeoNode datasets
        const datasetsResponse = await fetchDatasets();
        const datasets = datasetsResponse.datasets || [];

        // Convert to layer discovery format
        const geoNodeLayers = datasets.map(datasetToLayerDiscovery);

        // Fetch WMS capabilities
        const wmsLayers = await getFullDiscovery();
        const wmsLayerNames = new Set(wmsLayers.map(layer => layer.name));

        // Perform detailed matching analysis
        const exactMatches = [];
        const missingInWMS = [];
        const wmsOnlyLayers = [];

        // Check GeoNode layers against WMS
        for (const geoNodeLayer of geoNodeLayers) {
            if (wmsLayerNames.has(geoNodeLayer.name)) {
                exactMatches.push({
                    name: geoNodeLayer.name,
                    title: geoNodeLayer.title,
                    workspace: geoNodeLayer.workspace
                });
            } else {
                missingInWMS.push({
                    name: geoNodeLayer.name,
                    title: geoNodeLayer.title,
                    workspace: geoNodeLayer.workspace
                });
            }
        }

        // Find WMS layers not in GeoNode
        const geoNodeLayerNames = new Set(geoNodeLayers.map(layer => layer.name));
        for (const wmsLayer of wmsLayers) {
            if (!geoNodeLayerNames.has(wmsLayer.name)) {
                wmsOnlyLayers.push({
                    name: wmsLayer.name,
                    title: wmsLayer.title
                });
            }
        }

        const matchPercentage = (exactMatches.length / geoNodeLayers.length) * 100;

        console.log(`API: Validation complete - ${exactMatches.length}/${geoNodeLayers.length} layers match (${matchPercentage.toFixed(1)}%)`);

        res.json({
            success: true,
            summary: {
                totalGeoNodeLayers: geoNodeLayers.length,
                totalWMSLayers: wmsLayers.length,
                exactMatches: exactMatches.length,
                missingInWMS: missingInWMS.length,
                wmsOnlyLayers: wmsOnlyLayers.length,
                matchPercentage: Math.round(matchPercentage * 100) / 100
            },
            exactMatches: exactMatches.slice(0, 10), // First 10 for readability
            missingInWMS: missingInWMS.slice(0, 10), // First 10 for readability
            wmsOnlyLayers: wmsOnlyLayers.slice(0, 10), // First 10 for readability
            recommendations: {
                status: matchPercentage >= 90 ? 'EXCELLENT' : matchPercentage >= 70 ? 'GOOD' : 'NEEDS_IMPROVEMENT',
                message: matchPercentage >= 90
                    ? 'Layer name matching is excellent. Most layers should render correctly.'
                    : matchPercentage >= 70
                    ? 'Layer name matching is good but some layers may not render.'
                    : 'Layer name matching needs improvement. Many layers may not render correctly.'
            }
        });

    } catch (error: any) {
        console.error('API: Error in WMS validation endpoint:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// ===== EXISTING ROUTES =====

/**
 * @swagger
 * /api/datasets:
 *   get:
 *     summary: Get all datasets or search with parameters
 *     tags: [Datasets]
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for dataset names and descriptions
 *       - in: query
 *         name: temporal
 *         schema:
 *           type: boolean
 *         description: Filter by temporal datasets only
 *       - in: query
 *         name: queryable
 *         schema:
 *           type: boolean
 *         description: Filter by queryable datasets only
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Maximum number of results
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Number of results to skip
 *     responses:
 *       200:
 *         description: List of datasets
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 datasets:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Dataset'
 *                 total:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 offset:
 *                   type: integer
 *       500:
 *         description: Failed to fetch datasets
 */
// GET /api/datasets - Get all datasets or search with parameters
router.get('/', datasetController.getAllDatasets.bind(datasetController));

/**
 * @swagger
 * /api/datasets/search:
 *   get:
 *     summary: Advanced dataset search
 *     tags: [Datasets]
 *     parameters:
 *       - in: query
 *         name: q
 *         schema:
 *           type: string
 *         description: Search query
 *       - in: query
 *         name: keywords
 *         schema:
 *           type: string
 *         description: Comma-separated keywords
 *       - in: query
 *         name: temporal
 *         schema:
 *           type: boolean
 *         description: Filter temporal datasets
 *       - in: query
 *         name: queryable
 *         schema:
 *           type: boolean
 *         description: Filter queryable datasets
 *     responses:
 *       200:
 *         description: Search results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Dataset'
 *                 total:
 *                   type: integer
 *                 query:
 *                   type: string
 */
// GET /api/datasets/search - Advanced search (explicit search endpoint)
router.get('/search', datasetController.searchDatasets.bind(datasetController));

/**
 * @swagger
 * /api/datasets/popular:
 *   get:
 *     summary: Get popular datasets
 *     tags: [Datasets]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of popular datasets to return
 *     responses:
 *       200:
 *         description: List of popular datasets
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Dataset'
 */
// GET /api/datasets/popular - Get popular datasets
router.get('/popular', datasetController.getPopularDatasets.bind(datasetController));

/**
 * @swagger
 * /api/datasets/recent:
 *   get:
 *     summary: Get recently updated datasets
 *     tags: [Datasets]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of recent datasets to return
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           default: 30
 *         description: Number of days to look back
 *     responses:
 *       200:
 *         description: List of recently updated datasets
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Dataset'
 */
// GET /api/datasets/recent - Get recently updated datasets
router.get('/recent', datasetController.getRecentDatasets.bind(datasetController));

/**
 * @swagger
 * /api/datasets/keyword/{keyword}:
 *   get:
 *     summary: Get datasets by keyword
 *     tags: [Datasets]
 *     parameters:
 *       - in: path
 *         name: keyword
 *         required: true
 *         schema:
 *           type: string
 *         description: Keyword to search for
 *     responses:
 *       200:
 *         description: Datasets matching the keyword
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Dataset'
 *       404:
 *         description: No datasets found for keyword
 */
// GET /api/datasets/keyword/:keyword - Get datasets by keyword
router.get('/keyword/:keyword', datasetController.getDatasetsByKeyword.bind(datasetController));

/**
 * @swagger
 * /api/datasets/cache/clear:
 *   post:
 *     summary: Clear dataset cache
 *     tags: [Datasets]
 *     description: Administrative endpoint to clear dataset cache
 *     responses:
 *       200:
 *         description: Cache cleared successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       500:
 *         description: Failed to clear cache
 */
// POST /api/datasets/cache/clear - Clear cache (admin)
router.post('/cache/clear', datasetController.clearCache.bind(datasetController));

/**
 * @swagger
 * /api/datasets/{id}:
 *   get:
 *     summary: Get specific dataset by ID
 *     tags: [Datasets]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Dataset ID
 *     responses:
 *       200:
 *         description: Dataset details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Dataset'
 *       404:
 *         description: Dataset not found
 *       500:
 *         description: Failed to fetch dataset
 */
// GET /api/datasets/:id - Get specific dataset by ID
router.get('/:id', datasetController.getDatasetById.bind(datasetController));

/**
 * @swagger
 * /api/datasets/{id}/metadata:
 *   get:
 *     summary: Get dataset metadata
 *     tags: [Datasets]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Dataset ID
 *     responses:
 *       200:
 *         description: Dataset metadata
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                 metadata:
 *                   type: object
 *                 schema:
 *                   type: object
 *                 capabilities:
 *                   type: object
 *       404:
 *         description: Dataset not found
 *       500:
 *         description: Failed to fetch metadata
 */
// GET /api/datasets/:id/metadata - Get dataset metadata
router.get('/:id/metadata', datasetController.getDatasetMetadata.bind(datasetController));

/**
 * @swagger
 * /api/datasets/{id}/export:
 *   get:
 *     summary: Export dataset in various formats
 *     tags: [Datasets]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Dataset ID
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [json, csv, shapefile, geojson, kml]
 *           default: json
 *         description: Export format
 *       - in: query
 *         name: bbox
 *         schema:
 *           type: string
 *         description: Bounding box for spatial filtering
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for temporal filtering
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for temporal filtering
 *     responses:
 *       200:
 *         description: Exported dataset
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *           text/csv:
 *             schema:
 *               type: string
 *           application/zip:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: Dataset not found
 *       500:
 *         description: Export failed
 */
// GET /api/datasets/:id/export - Export dataset in various formats
router.get('/:id/export', datasetController.exportDataset.bind(datasetController));

export { router as datasetRouter };
