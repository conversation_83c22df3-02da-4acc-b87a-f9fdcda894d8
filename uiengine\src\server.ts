import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { createServer } from 'http';
import swaggerUi from 'swagger-ui-express';
import swaggerSpec from './swagger';
import { owsRouter } from './routes/ows';
import { datasetRouter } from './routes/datasets';
// Phase 3: Real-time Alerting System imports
import alertRulesRouter from './routes/alertRules';
import alertEventsRouter from './routes/alertEvents';
import { AlertEngine } from './services/alertEngine';
import { AlertSocket } from './websocket/alertSocket';
import { NotificationService } from './services/notificationService';
import { DatabaseService } from './services/databaseService';

dotenv.config();

// Conditionally import PostGIS-related modules
let roiRouter: any = null;
let testConnection: any = null;
let checkPostGIS: any = null;
let closeConnection: any = null;

// Check if PostGIS should be enabled
const ENABLE_POSTGIS = process.env.ENABLE_POSTGIS !== 'false' && process.env.NODE_ENV !== 'minimal';

if (ENABLE_POSTGIS) {
  try {
    const roiModule = require('./routes/roi');
    const dbModule = require('./config/database');
    
    roiRouter = roiModule.default;
    testConnection = dbModule.testConnection;
    checkPostGIS = dbModule.checkPostGIS;
    closeConnection = dbModule.closeConnection;
    
    console.log('✅ PostGIS modules loaded successfully');
  } catch (error) {    console.warn('⚠️ PostGIS modules not available, running without spatial features:', (error as Error).message);
    console.log('💡 To enable PostGIS features, ensure database dependencies are installed');
  }
}

const app = express();
const port = process.env.PORT || 3001; 

// Phase 3: Global alerting system variables
let alertSocket: AlertSocket | null = null;
let notificationService: NotificationService | null = null;
let alertEngine: AlertEngine | null = null;

// Middleware
app.use(cors());
app.use(express.json());

/**
 * @swagger
 * tags:
 *   - name: OWS
 *     description: OGC Web Services (WMS, WFS) proxy endpoints
 *   - name: Datasets
 *     description: Dataset management and discovery
 *   - name: Alerts
 *     description: Alert rules and events management
 *   - name: Reports
 *     description: Report generation and management
 *   - name: ROI
 *     description: Region of Interest operations (PostGIS)
 */

// Routes
app.use('/api/ows', owsRouter);
app.use('/api/datasets', datasetRouter);
app.use('/api/alert-rules', alertRulesRouter);
app.use('/api/alert-events', alertEventsRouter);
app.use('/api/reports', require('./routes/reports').default); // Phase 4: Report routes

// Swagger API documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
app.get('/api-docs.json', (req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.send(swaggerSpec);
});


// Conditionally add PostGIS routes if available
if (roiRouter) {
  app.use('/api/roi', roiRouter);
} else {
  // Provide fallback endpoints that explain PostGIS is not available
  app.use('/api/roi', (req, res) => {
    res.status(503).json({
      success: false,
      error: 'PostGIS features not available',
      message: 'PostGIS database is not configured. Please check your database connection and ensure PostGIS dependencies are installed.',
      fallback: true
    });
  });
  console.log('PostGIS not available - ROI endpoints will return 503');
}

/**
 * @swagger
 * /health:
 *   get:
 *     summary: Check the health status of the API and its dependencies
 *     tags: [System]
 *     responses:
 *       200:
 *         description: Health check information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: healthy
 *                 services:
 *                   type: object
 *                   properties:
 *                     database:
 *                       type: string
 *                       example: healthy
 *                     postgis:
 *                       type: string
 *                       example: healthy
 *                     datasets:
 *                       type: string
 *                       example: healthy
 *                 features:
 *                   type: object
 *                   properties:
 *                     core:
 *                       type: boolean
 *                       example: true
 *                     spatial:
 *                       type: boolean
 *                       example: true
 *                     alerts:
 *                       type: boolean
 *                       example: true
 *                     analytics:
 *                       type: boolean
 *                       example: true
 *                     reports:
 *                       type: boolean
 *                       example: true
 *                     advanced_spatial:
 *                       type: boolean
 *                       example: true
 */
// Health check endpoint
app.get('/health', async (req, res) => {
  let dbStatus = false;
  let postgisStatus = false;
  let datasetStatus = 'checking';
  
  if (testConnection && checkPostGIS) {
    try {
      dbStatus = await testConnection();
      postgisStatus = await checkPostGIS();
    } catch (error) {
      console.warn('Database health check failed:', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  // Check dataset service
  try {
    const { datasetService } = await import('./services/datasetService');
    await datasetService.getAllDatasets();
    datasetStatus = 'healthy';
  } catch (error) {
    datasetStatus = 'unavailable';
    console.warn('Dataset service health check failed:', (error as Error).message);
  }
    res.status(200).json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    database: dbStatus ? 'connected' : 'disconnected',
    postgis: postgisStatus ? 'available' : 'unavailable',
    datasets: datasetStatus,
    features: {
      core: true, // Core WMS features always available
      spatial: !!roiRouter,
      alerts: true, // Alerts available (fallback mode if no DB)
      analytics: true, // Analytics available (fallback mode if no DB)
      reports: dbStatus, // Report generation requires database
      advanced_spatial: postgisStatus // Advanced PostGIS features
    },
    dataMode: dbStatus ? 'full' : 'degraded',
    message: dbStatus 
      ? 'All features operational' 
      : 'Core features operational - Database features in fallback mode'
  });
});

// Error handling middleware
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

const server = createServer(app);

// Graceful shutdown
process.on('SIGTERM', async () => {
  if (closeConnection) {
    try {
      await closeConnection();
    } catch (error) {
      console.warn('Warning during database shutdown:', error instanceof Error ? error.message : 'Unknown error');
    }
  }
  process.exit(0);
});

process.on('SIGINT', async () => {  
  // Shutdown alerting system components
  if (alertEngine) {
    (alertEngine as AlertEngine).stop();
    console.log('Alert engine stopped');
  }
  
  if (alertSocket) {
    (alertSocket as AlertSocket).close();
    console.log('WebSocket server closed');
  }
  
  // Close database connections
  if (closeConnection) {
    try {
      await closeConnection();
    } catch (error) {
      console.warn('Warning during database shutdown:', error instanceof Error ? error.message : 'Unknown error');
    }
  }
  
  try {
    await DatabaseService.closePool();
    console.log('Database pool closed');
  } catch (error) {
    console.warn('Warning during database pool shutdown:', error instanceof Error ? error.message : 'Unknown error');
  }
  
  process.exit(0);
});

server.listen(port, async () => {
  console.log(`Server is running on port ${port}`);
  
  // Test database connection on startup (non-blocking)
  if (testConnection && checkPostGIS) {
    try {
      await testConnection();
      await checkPostGIS();
      console.log('PostGIS integration verified and ready');
    } catch (error) {
    }
  } else {
    console.log('ℹRunning in minimal mode without PostGIS dependencies');
  }
  // Phase 3: Initialize Real-time Alerting System
  try {
    // Initialize WebSocket server for real-time alerts
    alertSocket = new AlertSocket(server);
    console.log('WebSocket server initialized for real-time alerts');    // Initialize notification service
    notificationService = new NotificationService();
    notificationService.setSocketIO(alertSocket.getIO());
    await notificationService.testNotifications();
    console.log('Notification service initialized');

    // Initialize alert engine (will use fallback mode if database unavailable)
    alertEngine = new AlertEngine();
    
    // Test database connection for alert engine
    const dbConnected = await DatabaseService.testConnection();
    if (dbConnected) {
      // Start alert engine with database features
      await alertEngine.start(1);
      console.log('Alert engine started with database features (1-minute polling interval)');
    } else {
      // Start alert engine in fallback mode
      await alertEngine.startFallbackMode(1);
      console.log('Alert engine started in fallback mode (in-memory only)');
    }

  } catch (error) {
    console.error('Failed to initialize alerting system:', error);
  }
});
