# UIEngine Complete Endpoint Testing Script
# Run this script to test all available endpoints

param(
    [string]$BaseUrl = "http://localhost:3001",
    [switch]$Verbose
)

$ErrorActionPreference = "Continue"
$results = @()

function Test-Endpoint {
    param(
        [string]$Url,
        [string]$Method = "GET",
        [hashtable]$Body = $null,
        [string]$Description
    )
    
    Write-Host "`n🔍 Testing: $Description" -ForegroundColor Cyan
    Write-Host "   URL: $Url" -ForegroundColor Gray
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
            TimeoutSec = 30
        }
        
        if ($Body) {
            $params.Body = ($Body | ConvertTo-Json)
            $params.ContentType = "application/json"
        }
        
        $response = Invoke-RestMethod @params
        Write-Host "   ✅ SUCCESS" -ForegroundColor Green
        
        if ($Verbose) {
            Write-Host "   Response:" -ForegroundColor Yellow
            $response | ConvertTo-Json -Depth 3 | Write-Host
        }
        
        $script:results += [PSCustomObject]@{
            Endpoint = $Description
            URL = $Url
            Method = $Method
            Status = "SUCCESS"
            Error = $null
        }
        
        return $response
    }
    catch {
        Write-Host "   ❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
        
        $script:results += [PSCustomObject]@{
            Endpoint = $Description
            URL = $Url
            Method = $Method
            Status = "FAILED"
            Error = $_.Exception.Message
        }
        
        return $null
    }
}

Write-Host "🚀 UIEngine Endpoint Testing Started" -ForegroundColor Magenta
Write-Host "Base URL: $BaseUrl" -ForegroundColor White

# 1. Health Check
Test-Endpoint -Url "$BaseUrl/health" -Description "Health Check"

# 2. API Documentation
Test-Endpoint -Url "$BaseUrl/api-docs.json" -Description "Swagger API Documentation"

# 3. OWS Endpoints
Write-Host "`n📡 Testing OWS (OGC Web Services) Endpoints..." -ForegroundColor Yellow
Test-Endpoint -Url "$BaseUrl/api/ows/capabilities" -Description "WMS/WFS Capabilities"
Test-Endpoint -Url "$BaseUrl/api/ows/features?typeName=geonode:africa_mosaic_optmised&maxFeatures=10" -Description "WFS Features"
Test-Endpoint -Url "$BaseUrl/api/ows/legend?layer=geonode:africa_mosaic_optmised&format=image/png" -Description "WMS Legend"
Test-Endpoint -Url "$BaseUrl/api/ows/search?query=africa" -Description "Spatial Search"

# 4. Dataset Endpoints
Write-Host "`n📊 Testing Dataset Management Endpoints..." -ForegroundColor Yellow
Test-Endpoint -Url "$BaseUrl/api/datasets" -Description "List All Datasets"
Test-Endpoint -Url "$BaseUrl/api/datasets/summary" -Description "Dataset Summary"
Test-Endpoint -Url "$BaseUrl/api/datasets/search?query=flood" -Description "Search Datasets"

# 5. Alert Rule Endpoints
Write-Host "`n🚨 Testing Alert System Endpoints..." -ForegroundColor Yellow
Test-Endpoint -Url "$BaseUrl/api/alert-rules" -Description "List Alert Rules"
Test-Endpoint -Url "$BaseUrl/api/alert-events" -Description "List Alert Events"

# Create a test alert rule
$testAlertRule = @{
    name = "Test Water Level Alert"
    description = "Test alert for high water levels"
    dataset_name = "flood:water_levels"
    field_name = "water_height"
    threshold_value = 5.0
    threshold_operator = "greater_than"
    is_active = $true
    polling_interval_minutes = 5
    notification_email = "<EMAIL>"
}

$createdRule = Test-Endpoint -Url "$BaseUrl/api/alert-rules" -Method "POST" -Body $testAlertRule -Description "Create Test Alert Rule"

if ($createdRule -and $createdRule.data -and $createdRule.data.id) {
    $ruleId = $createdRule.data.id
    Test-Endpoint -Url "$BaseUrl/api/alert-rules/$ruleId" -Description "Get Alert Rule by ID"
    Test-Endpoint -Url "$BaseUrl/api/alert-rules/$ruleId" -Method "DELETE" -Description "Delete Test Alert Rule"
}

# 6. Report Endpoints
Write-Host "`n📋 Testing Report Generation Endpoints..." -ForegroundColor Yellow
Test-Endpoint -Url "$BaseUrl/api/reports" -Description "List Available Reports"

# 7. ROI Endpoints (if PostGIS enabled)
Write-Host "`n🗺️ Testing ROI (Region of Interest) Endpoints..." -ForegroundColor Yellow
Test-Endpoint -Url "$BaseUrl/api/roi" -Description "List ROIs"

# Create a test ROI
$testROI = @{
    name = "Test Region"
    description = "Test region for endpoint testing"
    geometry = @{
        type = "Polygon"
        coordinates = @(@(@(27.5, -26.5), @(28.5, -26.5), @(28.5, -27.5), @(27.5, -27.5), @(27.5, -26.5)))
    }
    created_by = "test_user"
}

$createdROI = Test-Endpoint -Url "$BaseUrl/api/roi" -Method "POST" -Body $testROI -Description "Create Test ROI"

if ($createdROI -and $createdROI.data -and $createdROI.data.id) {
    $roiId = $createdROI.data.id
    Test-Endpoint -Url "$BaseUrl/api/roi/$roiId" -Description "Get ROI by ID"
    Test-Endpoint -Url "$BaseUrl/api/roi/$roiId" -Method "DELETE" -Description "Delete Test ROI"
}

# 8. Discovery Endpoints
Write-Host "`n🔍 Testing Discovery Endpoints..." -ForegroundColor Yellow
Test-Endpoint -Url "$BaseUrl/api/discoveries" -Description "Layer Discovery"

# Summary Report
Write-Host "`n📊 TEST SUMMARY REPORT" -ForegroundColor Magenta
Write-Host "=" * 50 -ForegroundColor White

$successCount = ($results | Where-Object { $_.Status -eq "SUCCESS" }).Count
$failCount = ($results | Where-Object { $_.Status -eq "FAILED" }).Count
$totalCount = $results.Count

Write-Host "Total Endpoints Tested: $totalCount" -ForegroundColor White
Write-Host "Successful: $successCount" -ForegroundColor Green
Write-Host "Failed: $failCount" -ForegroundColor Red
Write-Host "Success Rate: $([math]::Round(($successCount / $totalCount) * 100, 2))%" -ForegroundColor Yellow

if ($failCount -gt 0) {
    Write-Host "`n❌ FAILED ENDPOINTS:" -ForegroundColor Red
    $results | Where-Object { $_.Status -eq "FAILED" } | ForEach-Object {
        Write-Host "   • $($_.Endpoint): $($_.Error)" -ForegroundColor Red
    }
}

Write-Host "`n✅ SUCCESSFUL ENDPOINTS:" -ForegroundColor Green
$results | Where-Object { $_.Status -eq "SUCCESS" } | ForEach-Object {
    Write-Host "   • $($_.Endpoint)" -ForegroundColor Green
}

# Export results
$results | Export-Csv -Path "endpoint-test-results.csv" -NoTypeInformation
Write-Host "`n📄 Detailed results exported to: endpoint-test-results.csv" -ForegroundColor Cyan

Write-Host "`n🎉 Testing Complete!" -ForegroundColor Magenta
