import { Router } from 'express';
import { getCapabilities, getLayerMetadata } from '../controllers/owsController';
import { cacheMiddleware } from '../middleware/cache';
import axios from 'axios';
import { handleRemoteLayerRequest, RemoteLayerInfo } from '../services/remoteLayerService';
import { fetchDatasets, datasetToLayerDiscovery } from '../services/datasetsService';

// Helper interfaces for administrative boundaries
interface AdministrativeRegion {
    id: string;
    name: string;
    code?: string;
    properties?: Record<string, any>;
}

interface DistrictInfo {
    id: string;
    name: string;
    code: string;
    type: 'district_municipality' | 'metropolitan_municipality';
    municipalities: string[];
    isMetro: boolean;
}

interface MetroInfo {
    id: string;
    name: string;
    code: string;
    type: 'metropolitan_municipality';
    isMetro: true;
}

interface LocalMunicipalityInfo {
    id: string;
    name: string;
    code: string;
    parentDistrict: string;
    type: 'local_municipality';
}

interface WardInfo {
    id: string;
    name: string;
    tlc: string;
    coordinates: [number, number];
}

interface AdministrativeClassification {
    level: 'district_municipality' | 'metropolitan_municipality' | 'local_municipality';
    isMetro: boolean;
    parentDistrict?: string;
    code: string;
}

// Known South African Metropolitan Municipalities (8 total)
// These codes represent the 8 metropolitan municipalities in South Africa:
// JHB - City of Johannesburg Metropolitan Municipality
// TSH - City of Tshwane Metropolitan Municipality
// EKU - Ekurhuleni Metropolitan Municipality
// CPT - City of Cape Town Metropolitan Municipality
// ETH - eThekwini Metropolitan Municipality
// MAN - Mangaung Metropolitan Municipality
// BUF - Buffalo City Metropolitan Municipality
// NMA - Nelson Mandela Bay Metropolitan Municipality
const METRO_CODES = ['JHB', 'TSH', 'EKU', 'CPT', 'ETH', 'MAN', 'BUF', 'NMA'];
const METRO_NAMES: Record<string, string> = {
    'JHB': 'City of Johannesburg Metropolitan Municipality',
    'TSH': 'City of Tshwane Metropolitan Municipality',
    'EKU': 'Ekurhuleni Metropolitan Municipality',
    'CPT': 'City of Cape Town Metropolitan Municipality',
    'ETH': 'eThekwini Metropolitan Municipality',
    'MAN': 'Mangaung Metropolitan Municipality',
    'BUF': 'Buffalo City Metropolitan Municipality',
    'NMA': 'Nelson Mandela Bay Metropolitan Municipality'
};

/**
 * Administrative Level Classifier Helper
 * Determines the correct administrative level of a feature
 */
function classifyAdministrativeLevel(feature: any): AdministrativeClassification {
    const props = feature.properties;
    const code = props.adm2_id || props.ADM2_ID;
    const name = props.adm2_en || props.ADM2_EN;

    if (!code) {
        throw new Error('Missing administrative code');
    }

    // Check if it's a Metropolitan Municipality
    if (METRO_CODES.includes(code)) {
        return {
            level: 'metropolitan_municipality',
            isMetro: true,
            code: code
        };
    }

    // Check if it's a District Municipality (DC## pattern)
    if (code.startsWith('DC')) {
        return {
            level: 'district_municipality',
            isMetro: false,
            code: code
        };
    }

    // Otherwise, it's likely a Local Municipality under a district
    return {
        level: 'local_municipality',
        isMetro: false,
        parentDistrict: extractParentDistrictFromName(props),
        code: code
    };
}

/**
 * Extract parent district from municipality feature properties
 * Tries to find the parent district code from the feature's properties.
 */
function extractParentDistrictFromName(featureProps: Record<string, any>): string | undefined {
    // Try common parent district code fields
    return featureProps.adm2_id || featureProps.ADM2_ID || featureProps.district_code || featureProps.DISTRICT_CODE;
}

/**
 * District Extraction Helper
 * Separates district municipalities from metropolitan municipalities
 */
function extractDistricts(municipalData: any[]): {
    districtMunicipalities: DistrictInfo[];
    metropolitanMunicipalities: DistrictInfo[];
} {
    const districtMunicipalities: DistrictInfo[] = [];
    const metropolitanMunicipalities: DistrictInfo[] = [];
    const processedCodes = new Set<string>();

    municipalData.forEach(feature => {
        const classification = classifyAdministrativeLevel(feature);
        const props = feature.properties;
        const name = props.adm2_en || props.ADM2_EN;

        if (processedCodes.has(classification.code)) {
            return; // Skip duplicates
        }
        processedCodes.add(classification.code);

        const districtInfo: DistrictInfo = {
            id: classification.code.toLowerCase(),
            name: classification.isMetro ? METRO_NAMES[classification.code] || name : name,
            code: classification.code,
            type: classification.level as 'district_municipality' | 'metropolitan_municipality',
            municipalities: [],
            isMetro: classification.isMetro
        };

        if (classification.isMetro) {
            metropolitanMunicipalities.push(districtInfo);
        } else if (classification.level === 'district_municipality') {
            districtMunicipalities.push(districtInfo);
        }
    });

    return {
        districtMunicipalities: districtMunicipalities.sort((a, b) => a.name.localeCompare(b.name)),
        metropolitanMunicipalities: metropolitanMunicipalities.sort((a, b) => a.name.localeCompare(b.name))
    };
}

/**
    municipalData.forEach(feature => {
        const classification = classifyAdministrativeLevel(feature);
        const props = feature.properties;

        // Only include local municipalities that belong to this district
        // Use the adm2_id or ADM2_ID property to match the district code directly
        if (
            classification.level === 'local_municipality' &&
            (props.adm2_id === districtCode || props.ADM2_ID === districtCode)
        ) {
            localMunicipalities.push({
                id: (props.adm2_en || props.ADM2_EN).toLowerCase().replace(/\s+/g, '_'),
                name: props.adm2_en || props.ADM2_EN,
                code: classification.code,
                parentDistrict: districtCode,
                type: 'local_municipality'
            });
        }
    });
            localMunicipalities.push({
                id: (props.adm2_en || props.ADM2_EN).toLowerCase().replace(/\s+/g, '_'),
                name: props.adm2_en || props.ADM2_EN,
                code: classification.code,
                parentDistrict: districtCode,
                type: 'local_municipality'
            });
        }
    });

    return localMunicipalities.sort((a, b) => a.name.localeCompare(b.name));
}

/**
 * Hierarchy Validation Helper
 * Validates if a hierarchy path is correct for SA administrative structure
 */
function validateHierarchyPath(
    province: string,
    district?: string,
    municipality?: string
): {
    isValid: boolean;
    isMetroPath: boolean;
    skipDistrict: boolean;
} {
    // If district is a metro, municipality should be empty (metros skip to wards)
    if (district && METRO_CODES.includes(district)) {
        return {
            isValid: !municipality, // Valid only if no municipality selected
            isMetroPath: true,
            skipDistrict: false
        };
    }

    // For district municipalities, we need both district and municipality
    if (district && district.startsWith('DC')) {
        return {
            isValid: true,
            isMetroPath: false,
            skipDistrict: false
        };
    }

    return {
        isValid: true,
        isMetroPath: false,
        skipDistrict: false
    };
}

/**
 * Extract provinces from WFS GeoJSON response
 */
function extractProvincesFromGeoJSON(geoJsonData: any): AdministrativeRegion[] {
    const provinces: AdministrativeRegion[] = [];
    const uniqueProvinces = new Map<string, AdministrativeRegion>();

    if (geoJsonData?.features) {
        geoJsonData.features.forEach((feature: any) => {
            const props = feature.properties;

            // Try different possible field names for province
            const provinceName = props.PROVINCE || props.province || props.Province ||
                               props.NAME_1 || props.name_1 || props.ADM1_EN || props.adm1_en ||
                               props.NAME || props.name || props.PROV_NAME || props.prov_name ||
                               props.PROVNAME || props.provname;

            if (provinceName && !uniqueProvinces.has(provinceName)) {
                uniqueProvinces.set(provinceName, {
                    id: provinceName.toLowerCase().replace(/\s+/g, '_'),
                    name: provinceName,
                    code: props.PROV_CODE || props.prov_code || props.CODE || props.code,
                    properties: props
                });
            }
        });

        provinces.push(...Array.from(uniqueProvinces.values()));
    }

    // Sort provinces alphabetically
    provinces.sort((a, b) => a.name.localeCompare(b.name));
    return provinces;
}

/**
 * Extract provinces from WMS GetFeatureInfo response
 */
function extractProvincesFromFeatureInfo(featureInfoData: any): AdministrativeRegion[] {
    const provinces: AdministrativeRegion[] = [];
    const uniqueProvinces = new Map<string, AdministrativeRegion>();

    // Handle different response formats
    let features: any[] = [];

    if (featureInfoData?.features) {
        features = featureInfoData.features;
    } else if (Array.isArray(featureInfoData)) {
        features = featureInfoData;
    } else if (featureInfoData?.results) {
        features = featureInfoData.results;
    }

    features.forEach((feature: any) => {
        const props = feature.properties || feature;

        // Try different possible field names for province (based on actual data structure)
        const provinceName = props.adm1_en || props.ADM1_EN || // Primary field from test data
                           props.PROVINCE || props.province || props.Province ||
                           props.NAME_1 || props.name_1 ||
                           props.NAME || props.name || props.PROV_NAME || props.prov_name ||
                           props.PROVNAME || props.provname;

        if (provinceName && !uniqueProvinces.has(provinceName)) {
            uniqueProvinces.set(provinceName, {
                id: provinceName.toLowerCase().replace(/\s+/g, '_'),
                name: provinceName,
                code: props.PROV_CODE || props.prov_code || props.CODE || props.code,
                properties: props
            });
        }
    });

    provinces.push(...Array.from(uniqueProvinces.values()));
    provinces.sort((a, b) => a.name.localeCompare(b.name));
    return provinces;
}

/**
 * Extract municipalities from WFS GeoJSON response
 */
function extractMunicipalitiesFromGeoJSON(geoJsonData: any): AdministrativeRegion[] {
    const municipalities: AdministrativeRegion[] = [];
    const uniqueMunicipalities = new Map<string, AdministrativeRegion>();

    if (geoJsonData?.features) {
        geoJsonData.features.forEach((feature: any) => {
            const props = feature.properties;

            // Try different possible field names for municipality (based on actual data structure)
            const municipalityName = props.adm2_en || props.ADM2_EN || // Primary field from test data
                                   props.MUNICIPALITY || props.municipality || props.Municipality ||
                                   props.NAME_2 || props.name_2 ||
                                   props.MUNICNAME || props.municname || props.MUN_NAME || props.mun_name ||
                                   props.NAME || props.name || props.LOCAL_MUNICIPALITY || props.local_municipality;

            if (municipalityName && !uniqueMunicipalities.has(municipalityName)) {
                uniqueMunicipalities.set(municipalityName, {
                    id: municipalityName.toLowerCase().replace(/\s+/g, '_'),
                    name: municipalityName,
                    code: props.MUN_CODE || props.mun_code || props.CODE || props.code,
                    properties: props
                });
            }
        });

        municipalities.push(...Array.from(uniqueMunicipalities.values()));
    }

    // Sort municipalities alphabetically
    municipalities.sort((a, b) => a.name.localeCompare(b.name));
    return municipalities;
}

/**
 * Extract municipalities from WMS GetFeatureInfo response
 */
function extractMunicipalitiesFromFeatureInfo(featureInfoData: any, provinceName?: string): AdministrativeRegion[] {
    const municipalities: AdministrativeRegion[] = [];
    const uniqueMunicipalities = new Map<string, AdministrativeRegion>();

    // Handle different response formats
    let features: any[] = [];

    if (featureInfoData?.features) {
        features = featureInfoData.features;
    } else if (Array.isArray(featureInfoData)) {
        features = featureInfoData;
    } else if (featureInfoData?.results) {
        features = featureInfoData.results;
    }

    features.forEach((feature: any) => {
        const props = feature.properties || feature;

        // Check if this municipality belongs to the specified province (if filtering)
        if (provinceName) {
            const featureProvince = props.adm1_en || props.ADM1_EN || // Primary field from test data
                                  props.PROVINCE || props.province || props.Province ||
                                  props.NAME_1 || props.name_1 ||
                                  props.PROV_NAME || props.prov_name;

            if (featureProvince !== provinceName) {
                return; // Skip this municipality if it doesn't match the province filter
            }
        }

        // Try different possible field names for municipality (based on actual data structure)
        const municipalityName = props.adm2_en || props.ADM2_EN || // Primary field from test data
                               props.MUNICIPALITY || props.municipality || props.Municipality ||
                               props.NAME_2 || props.name_2 ||
                               props.MUNICNAME || props.municname || props.MUN_NAME || props.mun_name ||
                               props.NAME || props.name || props.LOCAL_MUNICIPALITY || props.local_municipality;

        if (municipalityName && !uniqueMunicipalities.has(municipalityName)) {
            uniqueMunicipalities.set(municipalityName, {
                id: municipalityName.toLowerCase().replace(/\s+/g, '_'),
                name: municipalityName,
                code: props.MUN_CODE || props.mun_code || props.CODE || props.code,
                properties: props
            });
        }
    });

    municipalities.push(...Array.from(uniqueMunicipalities.values()));
    municipalities.sort((a, b) => a.name.localeCompare(b.name));
    return municipalities;
}

/**
 * Extract districts from municipal boundaries data
 */
function extractDistrictsFromMunicipalData(geoJsonData: any, provinceName?: string): DistrictInfo[] {
    const districts: DistrictInfo[] = [];
    const districtMap = new Map<string, DistrictInfo>();

    if (geoJsonData?.features) {
        geoJsonData.features.forEach((feature: any) => {
            const props = feature.properties;

            // Get municipality info
            const municipalityName = props.adm2_en || props.ADM2_EN;
            const districtCode = props.adm2_id || props.ADM2_ID;
            const province = props.adm1_en || props.ADM1_EN;

            if (districtCode && municipalityName) {
                // Determine district type and name based on code pattern
                let districtName = '';
                let districtType: 'district_municipality' | 'metropolitan_municipality' = 'district_municipality';

                if (districtCode.startsWith('DC')) {
                    // District Council (e.g., DC44 -> "District Council 44")
                    const dcNumber = districtCode.substring(2);
                    districtName = `District Council ${dcNumber}`;
                    districtType = 'district_municipality';
                } else if (['JHB', 'TSH', 'EKU', 'CPT', 'ETH', 'MAN', 'BUF', 'NMA'].includes(districtCode)) {
                    // Metropolitan municipalities (no district level)
                    districtName = `${municipalityName} Metropolitan`;
                    districtType = 'metropolitan_municipality';
                } else {
                    // Other patterns - treat as district municipality
                    districtName = `District ${districtCode}`;
                    districtType = 'district_municipality';
                }

                // Create or update district entry
                if (!districtMap.has(districtCode)) {
                    districtMap.set(districtCode, {
                        id: districtCode.toLowerCase(),
                        name: districtName,
                        code: districtCode,
                        type: districtType,
                        municipalities: [],
                        isMetro: districtType === 'metropolitan_municipality'
                    });
                }

                // Add municipality to district
                const district = districtMap.get(districtCode)!;
                if (!district.municipalities.includes(municipalityName)) {
                    district.municipalities.push(municipalityName);
                }
            }
        });

        districts.push(...Array.from(districtMap.values()));
    }

    // Sort districts by name
    districts.sort((a, b) => a.name.localeCompare(b.name));
    return districts;
}

/**
 * Extract wards from place names data
 */
function extractWardsFromPlaceNames(geoJsonData: any, municipalityFilter?: string): WardInfo[] {
    const wards: WardInfo[] = [];
    const uniqueWards = new Map<string, WardInfo>();

    if (geoJsonData?.features) {
        geoJsonData.features.forEach((feature: any) => {
            const props = feature.properties;
            const geometry = feature.geometry;

            // Get ward info from place names
            const placeName = props.placename;
            const tlc = props.tlc; // Transitional Local Council
            const coordinates = geometry?.coordinates;

            if (tlc && placeName && coordinates) {
                // Create unique ward ID based on TLC
                const wardId = tlc.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');

                if (!uniqueWards.has(wardId)) {
                    uniqueWards.set(wardId, {
                        id: wardId,
                        name: tlc,
                        tlc: tlc,
                        coordinates: [coordinates[0], coordinates[1]]
                    });
                }
            }
        });

        wards.push(...Array.from(uniqueWards.values()));
    }

    // Sort wards by name
    wards.sort((a, b) => a.name.localeCompare(b.name));
    return wards;
}

/**
 * Extract wards from official SA wards 2020 layer
 */
function extractWardsFromSALayer(geoJsonData: any): WardInfo[] {
    const wards: WardInfo[] = [];

    if (!geoJsonData?.features) {
        return wards;
    }

    geoJsonData.features.forEach((feature: any) => {
        const props = feature.properties;
        const geometry = feature.geometry;

        // Extract ward information from SA wards layer
        const wardId = props.wardid;
        const wardLabel = props.wardlabel;
        const wardNo = props.wardno;
        const municipality = props.municipali; // Note: truncated field name
        const municipalityCode = props.cat_b;
        const district = props.district;
        const districtCode = props.districtco;
        const province = props.province;

        // Get center coordinates from geometry
        let coordinates: [number, number] = [0, 0];
        if (geometry?.coordinates) {
            // For MultiPolygon, get first polygon's first ring's first coordinate
            if (geometry.type === 'MultiPolygon' && geometry.coordinates[0]?.[0]?.[0]) {
                coordinates = [geometry.coordinates[0][0][0][0], geometry.coordinates[0][0][0][1]];
            } else if (geometry.type === 'Polygon' && geometry.coordinates[0]?.[0]) {
                coordinates = [geometry.coordinates[0][0][0], geometry.coordinates[0][0][1]];
            }
        }

        if (wardId && wardLabel) {
            wards.push({
                id: wardId,
                name: `Ward ${wardNo} (${wardLabel})`,
                tlc: municipality || 'Unknown Municipality',
                coordinates: coordinates
            } as WardInfo);
        }
    });

    // Sort wards by ward number
    wards.sort((a, b) => {
        // Extract ward number from name for sorting
        const aMatch = a.name.match(/Ward (\d+)/);
        const bMatch = b.name.match(/Ward (\d+)/);
        const aWardNo = aMatch ? parseInt(aMatch[1]) : 0;
        const bWardNo = bMatch ? parseInt(bMatch[1]) : 0;
        return aWardNo - bWardNo;
    });

    return wards;
}

const router = Router();

/**
 * @swagger
 * /api/ows/capabilities:
 *   get:
 *     summary: Get WMS capabilities from GeoServer
 *     tags: [OWS]
 *     responses:
 *       200:
 *         description: WMS capabilities XML document
 *         content:
 *           application/xml:
 *             schema:
 *               type: string
 *       500:
 *         description: Failed to fetch capabilities
 */
router.get('/capabilities', getCapabilities);

// WMS GetCapabilities
// router.get('/capabilities', cacheMiddleware(300), getCapabilities);

/**
 * @swagger
 * /api/ows/layer-metadata/{layerName}:
 *   get:
 *     summary: Get metadata for a specific layer
 *     tags: [OWS]
 *     parameters:
 *       - in: path
 *         name: layerName
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of the layer to get metadata for
 *     responses:
 *       200:
 *         description: Layer metadata information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 name:
 *                   type: string
 *                 title:
 *                   type: string
 *                 abstract:
 *                   type: string
 *                 queryable:
 *                   type: boolean
 *                 temporal:
 *                   type: boolean
 *       404:
 *         description: Layer not found
 *       500:
 *         description: Failed to fetch layer metadata
 */
// Individual layer metadata endpoint
router.get('/layer-metadata/:layerName', cacheMiddleware(300), getLayerMetadata);

/**
 * @swagger
 * /api/ows/wfs-proxy:
 *   get:
 *     summary: Proxy WFS requests to GeoServer
 *     tags: [OWS]
 *     description: Handles WFS GetCapabilities, DescribeFeatureType, and GetFeature requests
 *     parameters:
 *       - in: query
 *         name: SERVICE
 *         schema:
 *           type: string
 *           default: WFS
 *         description: OGC service type
 *       - in: query
 *         name: REQUEST
 *         required: true
 *         schema:
 *           type: string
 *           enum: [GetCapabilities, DescribeFeatureType, GetFeature]
 *         description: WFS request type
 *       - in: query
 *         name: VERSION
 *         schema:
 *           type: string
 *           default: "1.0.0"
 *         description: WFS version
 *       - in: query
 *         name: typeName
 *         schema:
 *           type: string
 *         description: Feature type name (for DescribeFeatureType and GetFeature)
 *       - in: query
 *         name: outputFormat
 *         schema:
 *           type: string
 *           enum: [application/json, application/xml, json]
 *         description: Output format for the response
 *       - in: query
 *         name: maxFeatures
 *         schema:
 *           type: integer
 *         description: Maximum number of features to return
 *       - in: query
 *         name: CQL_FILTER
 *         schema:
 *           type: string
 *         description: CQL filter expression
 *     responses:
 *       200:
 *         description: WFS response (XML or JSON based on outputFormat)
 *         content:
 *           application/xml:
 *             schema:
 *               type: string
 *           application/json:
 *             schema:
 *               type: object
 *       400:
 *         description: Invalid WFS request parameters
 *       500:
 *         description: Failed to fetch WFS data
 */
// WFS Proxy for GetCapabilities, DescribeFeatureType, and GetFeature requests
router.get('/wfs-proxy', cacheMiddleware(300), async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';

        // Ensure we have the required WFS service parameter
        const params = { ...req.query };
        if (!params.SERVICE) {
            params.SERVICE = 'WFS';
        }

        console.log('WFS Proxy request:', params);

        // Determine response type based on request type and output format
        let responseType: 'stream' | 'json' | 'text' = 'text';
        let acceptHeader = 'application/xml, text/xml';

        if (params.outputFormat === 'application/json' || params.outputFormat === 'json') {
            responseType = 'json';
            acceptHeader = 'application/json';
        }

        // Use /ows endpoint for WFS requests
        const response = await axios.get(`${geoserverUrl}/ows`, {
            params,
            responseType,
            httpsAgent: new (require('https').Agent)({
                rejectUnauthorized: false
            }),
            headers: {
                'Accept': acceptHeader
            },
            timeout: 30000 // 30 second timeout
        });

        console.log('WFS response received, content-type:', response.headers['content-type']);

        // Handle different response types
        if (responseType === 'json') {
            res.setHeader('Content-Type', 'application/json');
            res.json(response.data);
        } else {
            // Handle XML responses (GetCapabilities, DescribeFeatureType)
            res.setHeader('Content-Type', response.headers['content-type'] || 'application/xml');
            res.send(response.data);
        }

    } catch (error) {
        console.error('Error proxying WFS request:', error);
        if (error && typeof error === 'object' && 'response' in error) {
            const axiosError = error as any;
            console.error('GeoServer response status:', axiosError.response?.status);
            console.error('GeoServer response data:', axiosError.response?.data);
        }
        res.status(500).json({ error: 'Failed to fetch WFS data' });
    }
});



/**
 * @swagger
 * /api/ows/wms-proxy:
 *   get:
 *     summary: Proxy WMS requests to GeoServer
 *     tags: [OWS]
 *     description: Handles WMS GetMap, GetFeatureInfo, and GetLegendGraphic requests
 *     parameters:
 *       - in: query
 *         name: SERVICE
 *         schema:
 *           type: string
 *           default: WMS
 *         description: OGC service type
 *       - in: query
 *         name: REQUEST
 *         required: true
 *         schema:
 *           type: string
 *           enum: [GetMap, GetFeatureInfo, GetLegendGraphic]
 *         description: WMS request type
 *       - in: query
 *         name: VERSION
 *         schema:
 *           type: string
 *           default: "1.1.1"
 *         description: WMS version
 *       - in: query
 *         name: LAYERS
 *         schema:
 *           type: string
 *         description: Comma-separated list of layers
 *       - in: query
 *         name: STYLES
 *         schema:
 *           type: string
 *         description: Comma-separated list of styles
 *       - in: query
 *         name: FORMAT
 *         schema:
 *           type: string
 *           default: image/png
 *         description: Output format
 *       - in: query
 *         name: TRANSPARENT
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Whether the background should be transparent
 *       - in: query
 *         name: SRS
 *         schema:
 *           type: string
 *           default: EPSG:4326
 *         description: Spatial reference system
 *       - in: query
 *         name: BBOX
 *         schema:
 *           type: string
 *         description: Bounding box (minx,miny,maxx,maxy)
 *       - in: query
 *         name: WIDTH
 *         schema:
 *           type: integer
 *         description: Width of the output image
 *       - in: query
 *         name: HEIGHT
 *         schema:
 *           type: integer
 *         description: Height of the output image
 *       - in: query
 *         name: TIME
 *         schema:
 *           type: string
 *         description: Time dimension for temporal layers
 *     responses:
 *       200:
 *         description: WMS response (image or JSON based on request type)
 *         content:
 *           image/png:
 *             schema:
 *               type: string
 *               format: binary
 *           image/jpeg:
 *             schema:
 *               type: string
 *               format: binary
 *           application/json:
 *             schema:
 *               type: object
 *       400:
 *         description: Invalid WMS request parameters
 *       500:
 *         description: Failed to fetch WMS data
 */
// WMS Proxy for thumbnails, GetMap, and GetFeatureInfo requests
router.get('/wms-proxy', cacheMiddleware(300), async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';

        // Ensure we have the required WMS service parameter for various request types
        const params = { ...req.query };
        if (params.REQUEST === 'GetLegendGraphic' && !params.SERVICE) {
            params.SERVICE = 'WMS';
        }
        if (params.REQUEST === 'GetMap' && !params.SERVICE) {
            params.SERVICE = 'WMS';
        }
        if (params.REQUEST === 'GetFeatureInfo' && !params.SERVICE) {
            params.SERVICE = 'WMS';
        }

        // Check if this is a request for a remote layer
        const layerName = params.LAYERS || params.LAYER;
        if (layerName && typeof layerName === 'string') {
            console.log(`🔍 WMS Proxy: Checking if layer "${layerName}" is remote...`);

            try {
                // Fetch all datasets to check for remote layers
                const datasetsResponse = await fetchDatasets();
                const datasets = datasetsResponse.datasets || [];
                const allLayers = datasets.map(datasetToLayerDiscovery);

                // Find the layer in our dataset
                console.log(`🔍 WMS Proxy: Looking for layer "${layerName}" in ${allLayers.length} layers`);
                console.log(`🔍 WMS Proxy: Available layer names:`, allLayers.map(l => l.name));

                const layer = allLayers.find(l => l.name === layerName);

                // Also try to find by original name without workspace prefix
                const layerByOriginalName = !layer ? allLayers.find(l => {
                    const originalName = l.name.includes(':') ? l.name.split(':').pop() : l.name;
                    const searchName = layerName.includes(':') ? layerName.split(':').pop() : layerName;
                    return originalName === searchName;
                }) : null;

                const foundLayer = layer || layerByOriginalName;

                if (foundLayer && foundLayer.isRemote) {
                    console.log(`🌐 WMS Proxy: Handling remote layer "${layerName}" (found as "${foundLayer.name}") - Service: ${foundLayer.serviceType}`);

                    // Handle remote layer request
                    const remoteLayerInfo: RemoteLayerInfo = {
                        name: foundLayer.name,
                        title: foundLayer.title,
                        serviceType: foundLayer.serviceType,
                        remoteUrl: foundLayer.remoteUrl,
                        isRemote: true
                    };

                    const requestType = params.REQUEST as 'GetMap' | 'GetLegendGraphic' | 'GetCapabilities';
                    const remoteResponse = await handleRemoteLayerRequest(remoteLayerInfo, requestType, params as Record<string, string>);

                    // Forward the remote response
                    res.setHeader('Content-Type', remoteResponse.headers.get('content-type') || 'image/png');

                    // Convert response to buffer and send
                    const buffer = await remoteResponse.arrayBuffer();
                    res.send(Buffer.from(buffer));
                    return;
                } else if (foundLayer) {
                    console.log(`🏠 WMS Proxy: Handling local layer "${layerName}" (found as "${foundLayer.name}")`);
                } else {
                    console.log(`❌ WMS Proxy: Layer "${layerName}" not found in datasets`);
                }
            } catch (remoteError) {
                console.warn(`⚠️ WMS Proxy: Could not check remote layer status for "${layerName}":`, remoteError);
                // Continue with local processing
            }
        }

        // Determine response type based on request type
        let responseType: 'stream' | 'json' = 'stream';
        let acceptHeader = 'image/*';

        if (params.REQUEST === 'GetFeatureInfo') {
            responseType = 'json';
            acceptHeader = 'application/json, text/html, text/plain';
        }

        // Use /ows endpoint for WMS requests (not /wms)
        const response = await axios.get(`${geoserverUrl}/ows`, {
            params,
            responseType,
            httpsAgent: new (require('https').Agent)({
                rejectUnauthorized: false
            }),
            headers: {
                'Accept': acceptHeader
            }
        });

        // Handle GetFeatureInfo responses differently
        if (params.REQUEST === 'GetFeatureInfo') {
            res.setHeader('Content-Type', response.headers['content-type'] || 'application/json');
            res.json(response.data);
        } else {
            // Forward the content type for image responses
            res.setHeader('Content-Type', response.headers['content-type'] || 'image/jpeg');
            response.data.pipe(res);
        }
    } catch (error) {
        console.error('Error proxying WMS request:', error);
        if (error && typeof error === 'object' && 'response' in error) {
            const axiosError = error as any;
            console.error('GeoServer response status:', axiosError.response?.status);
            console.error('GeoServer response data:', axiosError.response?.data);
        }
        res.status(500).json({ error: 'Failed to fetch WMS data' });
    }
});

/**
 * @swagger
 * /api/ows/legend:
 *   get:
 *     summary: Get legend graphic for a layer
 *     tags: [OWS]
 *     description: Simplified endpoint for GetLegendGraphic requests
 *     parameters:
 *       - in: query
 *         name: layer
 *         required: true
 *         schema:
 *           type: string
 *         description: Layer name
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           default: image/png
 *           enum: [image/png, image/jpeg, image/gif]
 *         description: Output format
 *     responses:
 *       200:
 *         description: Legend graphic image
 *         content:
 *           image/png:
 *             schema:
 *               type: string
 *               format: binary
 *           image/jpeg:
 *             schema:
 *               type: string
 *               format: binary
 *           image/gif:
 *             schema:
 *               type: string
 *               format: binary
 *       400:
 *         description: Missing required parameters
 *       500:
 *         description: Failed to fetch legend graphic
 */
// Legend endpoint specifically for GetLegendGraphic requests
router.get('/legend', cacheMiddleware(300), async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';
        const { layer, format = 'image/png' } = req.query;

        if (!layer) {
            return res.status(400).json({ error: 'Layer parameter is required' });
        }

        const params = {
            SERVICE: 'WMS',
            REQUEST: 'GetLegendGraphic',
            LAYER: layer,
            FORMAT: format,
            VERSION: '1.1.1'
        };

        const response = await axios.get(`${geoserverUrl}/ows`, {
            params,
            responseType: 'stream',
            httpsAgent: new (require('https').Agent)({
                rejectUnauthorized: false
            }),
            headers: {
                'Accept': 'image/*'
            }
        });

        // Forward the content type
        res.setHeader('Content-Type', response.headers['content-type'] || format);
        response.data.pipe(res);
    } catch (error) {
        console.error('Error fetching legend graphic:', error);
        if (error && typeof error === 'object' && 'response' in error) {
            const axiosError = error as any;
            console.error('GeoServer legend response status:', axiosError.response?.status);
            console.error('GeoServer legend response data:', axiosError.response?.data);
        }
        res.status(500).json({ error: 'Failed to fetch legend graphic' });
    }
});

router.get('/layers', cacheMiddleware(300), getCapabilities);

/**
 * @swagger
 * /api/ows/download:
 *   get:
 *     summary: Download spatial data as shapefile
 *     tags: [OWS]
 *     description: Export spatial data in shapefile format with optional temporal and spatial filtering
 *     parameters:
 *       - in: query
 *         name: typeName
 *         required: true
 *         schema:
 *           type: string
 *         description: Feature type name to download
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for temporal filtering (YYYY-MM-DD)
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for temporal filtering (YYYY-MM-DD)
 *       - in: query
 *         name: bbox
 *         schema:
 *           type: string
 *         description: Bounding box for spatial filtering (minx,miny,maxx,maxy)
 *     responses:
 *       200:
 *         description: Shapefile ZIP archive
 *         content:
 *           application/zip:
 *             schema:
 *               type: string
 *               format: binary
 *         headers:
 *           Content-Disposition:
 *             description: Attachment filename
 *             schema:
 *               type: string
 *               example: "attachment; filename=flood-data.zip"
 *       400:
 *         description: Missing required parameters
 *       500:
 *         description: Failed to download data
 */
// Download route for shapefile exports
router.get('/download', cacheMiddleware(60), async (req, res) => {
    try {
        const { typeName, startDate, endDate, bbox } = req.query;
        const response = await axios.get(`${process.env.GEOSERVER_URL}/ows`, {
            params: {
                service: 'WFS',
                version: '1.0.0',
                request: 'GetFeature',
                typeName,
                outputFormat: 'SHAPE-ZIP',
                format_options: 'filename:flood-data.zip',
                CQL_FILTER: startDate && endDate ? `date>='${startDate}' AND date<='${endDate}'` : undefined,
                bbox
            },
            responseType: 'arraybuffer'
        });

        res.setHeader('Content-Type', 'application/zip');
        res.setHeader('Content-Disposition', 'attachment; filename=flood-data.zip');
        res.send(response.data);
    } catch (error) {
        console.error('Error downloading data:', error);
        res.status(500).json({ error: 'Failed to download data' });
    }
});

/**
 * @swagger
 * /api/ows/feature-info:
 *   get:
 *     summary: Get feature information at a specific point
 *     tags: [OWS]
 *     description: Dedicated endpoint for WMS GetFeatureInfo requests
 *     parameters:
 *       - in: query
 *         name: layers
 *         required: true
 *         schema:
 *           type: string
 *         description: Comma-separated list of layers
 *       - in: query
 *         name: query_layers
 *         required: true
 *         schema:
 *           type: string
 *         description: Comma-separated list of queryable layers
 *       - in: query
 *         name: x
 *         required: true
 *         schema:
 *           type: integer
 *         description: X coordinate in pixels
 *       - in: query
 *         name: y
 *         required: true
 *         schema:
 *           type: integer
 *         description: Y coordinate in pixels
 *       - in: query
 *         name: width
 *         required: true
 *         schema:
 *           type: integer
 *         description: Width of the map in pixels
 *       - in: query
 *         name: height
 *         required: true
 *         schema:
 *           type: integer
 *         description: Height of the map in pixels
 *       - in: query
 *         name: bbox
 *         required: true
 *         schema:
 *           type: string
 *         description: Bounding box (minx,miny,maxx,maxy)
 *       - in: query
 *         name: srs
 *         schema:
 *           type: string
 *           default: EPSG:4326
 *         description: Spatial reference system
 *       - in: query
 *         name: crs
 *         schema:
 *           type: string
 *         description: Coordinate reference system (alternative to srs)
 *       - in: query
 *         name: info_format
 *         schema:
 *           type: string
 *           default: application/json
 *           enum: [application/json, text/html, text/plain]
 *         description: Format of the feature info response
 *       - in: query
 *         name: feature_count
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Maximum number of features to return
 *     responses:
 *       200:
 *         description: Feature information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *           text/html:
 *             schema:
 *               type: string
 *           text/plain:
 *             schema:
 *               type: string
 *       400:
 *         description: Missing required parameters
 *       500:
 *         description: Failed to fetch feature information
 */
// Dedicated GetFeatureInfo endpoint for queryable layers
router.get('/feature-info', async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';
        const {
            layers,
            query_layers,
            x,
            y,
            width,
            height,
            bbox,
            srs,
            crs,
            info_format = 'application/json',
            feature_count = 10
        } = req.query;

        // Validate required parameters
        if (!layers || !query_layers || !x || !y || !width || !height || !bbox) {
            return res.status(400).json({
                error: 'Missing required parameters: layers, query_layers, x, y, width, height, bbox'
            });
        }

        const params = {
            SERVICE: 'WMS',
            REQUEST: 'GetFeatureInfo',
            VERSION: '1.1.1',
            LAYERS: layers,
            QUERY_LAYERS: query_layers,
            X: x,
            Y: y,
            WIDTH: width,
            HEIGHT: height,
            BBOX: bbox,
            SRS: srs || crs || 'EPSG:4326',
            INFO_FORMAT: info_format,
            FEATURE_COUNT: feature_count
        };

        const response = await axios.get(`${geoserverUrl}/ows`, {
            params,
            httpsAgent: new (require('https').Agent)({
                rejectUnauthorized: false
            }),
            headers: {
                'Accept': 'application/json, text/html, text/plain'
            }
        });

        res.setHeader('Content-Type', response.headers['content-type'] || 'application/json');
        res.json(response.data);
    } catch (error) {
        console.error('Error fetching feature info:', error);
        if (error && typeof error === 'object' && 'response' in error) {
            const axiosError = error as any;
            console.error('GeoServer response status:', axiosError.response?.status);
            console.error('GeoServer response data:', axiosError.response?.data);
        }
        res.status(500).json({ error: 'Failed to fetch feature information' });
    }
});

/**
 * @swagger
 * /api/ows/aoi-screenshot:
 *   get:
 *     summary: Generate a screenshot of an area of interest
 *     tags: [OWS]
 *     description: Creates a map image for a specified area with selected layers
 *     parameters:
 *       - in: query
 *         name: bbox
 *         required: true
 *         schema:
 *           type: string
 *         description: Bounding box in format west,south,east,north
 *       - in: query
 *         name: layers
 *         required: true
 *         schema:
 *           type: string
 *         description: Comma-separated list of layers to include
 *       - in: query
 *         name: width
 *         schema:
 *           type: integer
 *           default: 800
 *         description: Width of the output image in pixels
 *       - in: query
 *         name: height
 *         schema:
 *           type: integer
 *           default: 600
 *         description: Height of the output image in pixels
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           default: png
 *           enum: [png, jpeg, gif]
 *         description: Output image format
 *       - in: query
 *         name: time
 *         schema:
 *           type: string
 *         description: Time value for temporal layers (ISO format or range)
 *       - in: query
 *         name: styles
 *         schema:
 *           type: string
 *           default: ""
 *         description: Comma-separated list of styles to apply
 *       - in: query
 *         name: srs
 *         schema:
 *           type: string
 *           default: EPSG:4326
 *         description: Spatial reference system
 *       - in: query
 *         name: transparent
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Whether the background should be transparent
 *     responses:
 *       200:
 *         description: Map image
 *         content:
 *           image/png:
 *             schema:
 *               type: string
 *               format: binary
 *           image/jpeg:
 *             schema:
 *               type: string
 *               format: binary
 *           image/gif:
 *             schema:
 *               type: string
 *               format: binary
 *       400:
 *         description: Invalid parameters
 *       408:
 *         description: Screenshot generation timed out
 *       500:
 *         description: Failed to generate screenshot
 */
// AOI Screenshot endpoint for generating map images of selected areas
router.get('/aoi-screenshot', cacheMiddleware(300), async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';

        // Extract and validate parameters
        const {
            bbox,
            layers,
            width = '800',
            height = '600',
            format = 'png',
            time,
            styles = '',
            srs = 'EPSG:4326'
        } = req.query;

        // Validate required parameters
        if (!bbox) {
            return res.status(400).json({ error: 'bbox parameter is required' });
        }
        if (!layers) {
            return res.status(400).json({ error: 'layers parameter is required' });
        }

        // Parse and validate bbox format (west,south,east,north)
        const bboxParts = (bbox as string).split(',');
        if (bboxParts.length !== 4) {
            return res.status(400).json({ error: 'bbox must be in format: west,south,east,north' });
        }

        // Validate numeric values
        const [west, south, east, north] = bboxParts.map(Number);
        if (isNaN(west) || isNaN(south) || isNaN(east) || isNaN(north)) {
            return res.status(400).json({ error: 'bbox coordinates must be numeric' });
        }

        // Validate bbox bounds
        if (west >= east || south >= north) {
            return res.status(400).json({ error: 'Invalid bbox: west must be < east and south must be < north' });
        }

        // Prepare WMS GetMap parameters
        const wmsParams: any = {
            SERVICE: 'WMS',
            REQUEST: 'GetMap',
            VERSION: '1.1.1',
            LAYERS: layers,
            BBOX: bbox,
            WIDTH: width,
            HEIGHT: height,
            FORMAT: `image/${format}`,
            SRS: srs,
            STYLES: styles,
            TRANSPARENT: req.query.transparent || 'true' // Allow frontend to control transparency
        };

        // Add temporal parameter if provided
        if (time) {
            wmsParams.TIME = time as string;
        }

        console.log('Generating AOI screenshot with params:', wmsParams);

        // Make request to GeoServer
        const response = await axios.get(`${geoserverUrl}/ows`, {
            params: wmsParams,
            responseType: 'stream',
            httpsAgent: new (require('https').Agent)({
                rejectUnauthorized: false
            }),
            headers: {
                'Accept': 'image/*'
            },
            timeout: 30000 // 30 second timeout for image generation
        });

        // Set appropriate headers
        res.setHeader('Content-Type', response.headers['content-type'] || `image/${format}`);
        res.setHeader('Cache-Control', 'public, max-age=300'); // 5 minute cache

        // Stream the image response
        response.data.pipe(res);

    } catch (error) {
        console.error('Error generating AOI screenshot:', error);

        if (error && typeof error === 'object' && 'response' in error) {
            const axiosError = error as any;
            console.error('GeoServer response status:', axiosError.response?.status);
            console.error('GeoServer response data:', axiosError.response?.data);

            // Return appropriate error based on GeoServer response
            if (axiosError.response?.status === 400) {
                return res.status(400).json({ error: 'Invalid parameters for map generation' });
            } else if (axiosError.response?.status === 404) {
                return res.status(404).json({ error: 'One or more layers not found' });
            }
        }

        // Check for timeout errors
        if (error && typeof error === 'object' && 'code' in error && error.code === 'ECONNABORTED') {
            return res.status(408).json({ error: 'Screenshot generation timed out' });
        }

        res.status(500).json({ error: 'Failed to generate AOI screenshot' });
    }
});

/**
 * @swagger
 * /api/ows/administrative-boundaries/provinces:
 *   get:
 *     summary: Get all provinces from administrative boundary layers
 *     tags: [Administrative Boundaries]
 *     description: Cascades through WFS → WMS → REST API to get province list
 *     responses:
 *       200:
 *         description: Provinces retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       code:
 *                         type: string
 *                 source:
 *                   type: string
 *                   enum: [WFS, WMS, REST]
 *       500:
 *         description: All methods failed to retrieve provinces
 */
router.get('/administrative-boundaries/provinces', cacheMiddleware(300), async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';
        const layerName = 'geonode:south_africa_provincial_boundaries';

        console.log('🏛️ Loading all 9 provinces (optimized approach)...');

        const wfsResponse = await axios.get(`${geoserverUrl}/ows`, {
            params: {
                SERVICE: 'WFS',
                REQUEST: 'GetFeature',
                typeName: layerName,
                version: '1.0.0',
                outputFormat: 'application/json',
                propertyName: 'adm1_en,adm1_pcode' // Only get needed fields, exclude geometry
            },
            httpsAgent: new (require('https').Agent)({
                rejectUnauthorized: false
            }),
            timeout: 15000
        });

        const provinces = extractProvincesFromGeoJSON(wfsResponse.data);

        console.log(`✅ Successfully loaded ${provinces.length} provinces`);

        return res.json({
            success: true,
            data: provinces,
            source: 'WFS_OPTIMIZED',
            count: provinces.length,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('❌ Failed to load provinces:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve provinces',
            details: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        });
    }
});


/**
 * @swagger
 * /api/ows/administrative-boundaries/districts:
 *   get:
 *     summary: Get districts, optionally filtered by province
 *     tags: [Administrative Boundaries]
 *     description: Extract districts from municipal boundaries data
 *     parameters:
 *       - in: query
 *         name: province
 *         schema:
 *           type: string
 *         description: Filter districts by province name
 *     responses:
 *       200:
 *         description: Districts retrieved successfully
 *       500:
 *         description: Failed to retrieve districts
 */
router.get('/administrative-boundaries/districts', cacheMiddleware(300), async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';
        const layerName = 'geonode:south_africa_municipal_boundaries';
        const provinceName = req.query.province as string;

        console.log(`🏛️ Loading districts${provinceName ? ` for province: ${provinceName}` : ''}...`);

        // Get all municipal data to extract district patterns
        try {
            console.log('📡 Getting municipal data to extract districts...');

            const params: any = {
                SERVICE: 'WFS',
                REQUEST: 'GetFeature',
                typeName: layerName,
                version: '1.0.0',
                outputFormat: 'application/json',
                propertyName: 'province,district,municipali,cat_b,districtco' // Only get needed fields
            };

            // Add CQL filter for province if specified
            if (provinceName) {
                params.CQL_FILTER = `province='${provinceName}'`;
            }

            // Add province filter if specified
            if (provinceName) {
                params.CQL_FILTER = `adm1_en='${provinceName}'`;
            }

            const wfsResponse = await axios.get(`${geoserverUrl}/ows`, {
                params,
                httpsAgent: new (require('https').Agent)({
                    rejectUnauthorized: false
                }),
                timeout: 15000
            });

            // Use the new helper function to extract districts properly
            const { districtMunicipalities, metropolitanMunicipalities } = extractDistricts(wfsResponse.data.features);

            // Combine both types and sort
            const allDistricts = [...districtMunicipalities, ...metropolitanMunicipalities];
            allDistricts.sort((a, b) => a.name.localeCompare(b.name));

            console.log(`✅ Extracted ${allDistricts.length} districts from municipal data (${districtMunicipalities.length} district municipalities + ${metropolitanMunicipalities.length} metropolitan municipalities)`);

            return res.json({
                success: true,
                data: allDistricts,
                source: 'WFS_MUNICIPAL_EXTRACTION_ENHANCED',
                count: allDistricts.length,
                breakdown: {
                    districtMunicipalities: districtMunicipalities.length,
                    metropolitanMunicipalities: metropolitanMunicipalities.length,
                    total: allDistricts.length
                },
                filteredBy: provinceName || null,
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            console.error('❌ Failed to extract districts:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to extract districts from municipal data',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }

    } catch (error) {
        console.error('Error in districts endpoint:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve districts',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});

/**
 * @swagger
 * /api/ows/administrative-boundaries/municipalities:
 *   get:
 *     summary: Get municipalities, optionally filtered by province or district
 *     tags: [Administrative Boundaries]
 *     description: Cascades through WFS → WMS → REST API to get municipality list
 *     parameters:
 *       - in: query
 *         name: province
 *         schema:
 *           type: string
 *         description: Filter municipalities by province name
 *       - in: query
 *         name: district
 *         schema:
 *           type: string
 *         description: Filter municipalities by district code
 *     responses:
 *       200:
 *         description: Municipalities retrieved successfully
 *       500:
 *         description: All methods failed to retrieve municipalities
 */
router.get('/administrative-boundaries/municipalities', cacheMiddleware(300), async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';
        const layerName = 'geonode:south_africa_municipal_boundaries';
        const provinceName = req.query.province as string;
        const districtCode = req.query.district as string;

        console.log(`🏛️ Loading municipalities${provinceName ? ` for province: ${provinceName}` : ''}${districtCode ? ` for district: ${districtCode}` : ''} using CQL filter...`);

        const params: any = {
            SERVICE: 'WFS',
            REQUEST: 'GetFeature',
            typeName: layerName,
            version: '1.0.0',
            outputFormat: 'application/json',
            propertyName: 'province,district,municipali,cat_b,districtco' // Only get needed fields
        };

        // Build CQL filter based on parameters
        const filters = [];
        if (provinceName) {
            filters.push(`province='${provinceName}'`);
        }
        if (districtCode) {
            filters.push(`districtco='${districtCode}'`);
        }
        if (filters.length > 0) {
            params.CQL_FILTER = filters.join(' AND ');
        }

        const wfsResponse = await axios.get(`${geoserverUrl}/ows`, {
            params,
            httpsAgent: new (require('https').Agent)({
                rejectUnauthorized: false
            }),
            timeout: 15000
        });

        // Extract municipalities from response
        const municipalities = extractMunicipalitiesFromGeoJSON(wfsResponse.data);

        console.log(`✅ Successfully loaded ${municipalities.length} municipalities`);

        return res.json({
            success: true,
            data: municipalities,
            source: 'WFS_CQL_FILTERED',
            count: municipalities.length,
            filteredBy: { province: provinceName, district: districtCode },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('❌ Failed to load municipalities:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve municipalities',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});

/**
 * @swagger
 * /api/ows/administrative-boundaries/wards:
                params: {
                    SERVICE: 'WMS',
                    REQUEST: 'GetFeatureInfo',
                    VERSION: '1.1.1',
                    LAYERS: layerName,
                    QUERY_LAYERS: layerName,
                    INFO_FORMAT: 'application/json',
                    FEATURE_COUNT: 500,
                    WIDTH: 256,
                    HEIGHT: 256,
                    SRS: 'EPSG:4326',
                    BBOX: '16,-35,33,-22', // South Africa bounds
                    X: 128,
                    Y: 128
                },
                httpsAgent: new (require('https').Agent)({
                    rejectUnauthorized: false
                }),
                timeout: 15000
            });

            const municipalities = extractMunicipalitiesFromFeatureInfo(wmsResponse.data, provinceName);
            if (municipalities.length > 0) {
                console.log(`WMS succeeded: Found ${municipalities.length} municipalities`);
                return res.json({
                    success: true,
                    data: municipalities,
                    source: 'WMS',
                    count: municipalities.length,
                    filteredBy: provinceName || null
                });
            }

            throw new Error('WMS returned no municipalities');

        } catch (wmsError) {
            console.error('WMS failed for municipalities:', {
                error: wmsError instanceof Error ? wmsError.message : 'Unknown error',
                province: provinceName,
                layerName,
                // Log axios error details if available
                status: (wmsError as any)?.response?.status,
                statusText: (wmsError as any)?.response?.statusText,
                responseData: (wmsError as any)?.response?.data
            });
        }

        // All methods failed

        res.status(500).json({
            success: false,
            error: 'All methods failed to retrieve municipalities',
            attemptedMethods: ['WFS', 'WMS'],
            filteredBy: provinceName || null,
            message: 'No fallback data available - check GeoServer connectivity and layer configuration'
        });

    } catch (error) {
        console.error('Error in municipalities endpoint:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve municipalities',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});

/**
 * @swagger
 * /api/ows/administrative-boundaries/test:
 *   get:
 *     summary: Test basic access to administrative boundary layers
 *     tags: [Administrative Boundaries]
 *     description: Simple test to verify layer access and get sample data
 *     responses:
 *       200:
 *         description: Test results
 */
router.get('/administrative-boundaries/test', async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';

        const testResults: Record<string, any> = {};

        // Test 1: Basic WFS GetCapabilities
        try {
            const capabilitiesResponse = await axios.get(`${geoserverUrl}/ows`, {
                params: {
                    SERVICE: 'WFS',
                    REQUEST: 'GetCapabilities',
                    version: '1.0.0'
                },
                httpsAgent: new (require('https').Agent)({
                    rejectUnauthorized: false
                }),
                timeout: 10000
            });

            testResults.wfsCapabilities = {
                success: true,
                status: capabilitiesResponse.status,
                hasData: capabilitiesResponse.data.length > 0
            };
        } catch (error) {
            testResults.wfsCapabilities = {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }

        // Test 2: Municipal boundaries layer - get 1 feature
        try {
            const municipalResponse = await axios.get(`${geoserverUrl}/ows`, {
                params: {
                    SERVICE: 'WFS',
                    REQUEST: 'GetFeature',
                    typeName: 'geonode:south_africa_municipal_boundaries',
                    version: '1.0.0',
                    outputFormat: 'application/json',
                    maxFeatures: 1
                },
                httpsAgent: new (require('https').Agent)({
                    rejectUnauthorized: false
                }),
                timeout: 15000
            });

            const sampleFeature = municipalResponse.data?.features?.[0];
            testResults.municipalLayer = {
                success: true,
                status: municipalResponse.status,
                featureCount: municipalResponse.data?.features?.length || 0,
                sampleFields: sampleFeature ? Object.keys(sampleFeature.properties) : [],
                sampleProperties: sampleFeature?.properties || null
            };
        } catch (error) {
            testResults.municipalLayer = {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                status: (error as any)?.response?.status,
                responseData: (error as any)?.response?.data
            };
        }

        // Test 3: Provincial boundaries layer - get 1 feature
        try {
            const provincialResponse = await axios.get(`${geoserverUrl}/ows`, {
                params: {
                    SERVICE: 'WFS',
                    REQUEST: 'GetFeature',
                    typeName: 'geonode:south_africa_provincial_boundaries',
                    version: '1.0.0',
                    outputFormat: 'application/json',
                    maxFeatures: 1
                },
                httpsAgent: new (require('https').Agent)({
                    rejectUnauthorized: false
                }),
                timeout: 15000
            });

            const sampleFeature = provincialResponse.data?.features?.[0];
            testResults.provincialLayer = {
                success: true,
                status: provincialResponse.status,
                featureCount: provincialResponse.data?.features?.length || 0,
                sampleFields: sampleFeature ? Object.keys(sampleFeature.properties) : [],
                sampleProperties: sampleFeature?.properties || null
            };
        } catch (error) {
            testResults.provincialLayer = {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                status: (error as any)?.response?.status,
                responseData: (error as any)?.response?.data
            };
        }

        res.json({
            success: true,
            geoserverUrl,
            testResults,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Error in administrative boundaries test:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to run administrative boundaries test',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});

/**
 * @swagger
 * /api/ows/administrative-boundaries/debug-municipalities:
 *   get:
 *     summary: Debug municipalities data structure
 *     tags: [Administrative Boundaries]
 *     description: Get raw municipality data to debug field structure
 *     parameters:
 *       - in: query
 *         name: province
 *         schema:
 *           type: string
 *         description: Filter by province name
 *       - in: query
 *         name: maxFeatures
 *         schema:
 *           type: integer
 *           default: 5
 *         description: Maximum features to return
 *     responses:
 *       200:
 *         description: Raw municipality data for debugging
 */
router.get('/administrative-boundaries/debug-municipalities', async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';
        const layerName = 'geonode:south_africa_municipal_boundaries';
        const provinceName = req.query.province as string;
        const maxFeatures = parseInt(req.query.maxFeatures as string) || 5;

        console.log(`🔍 Debug municipalities: province=${provinceName}, maxFeatures=${maxFeatures}`);

        // Try different output formats to see which one works
        const outputFormats = ['application/json', 'json', 'text/javascript'];
        let workingParams: any = null;
        let workingResponse: any = null;

        for (const format of outputFormats) {
            try {
                const params: any = {
                    SERVICE: 'WFS',
                    REQUEST: 'GetFeature',
                    typeName: layerName,
                    version: '1.0.0',
                    outputFormat: format,
                    maxFeatures
                };

                // Add province filter if specified (simplified to use only the correct field)
                if (provinceName) {
                    params.CQL_FILTER = `adm1_en='${provinceName}'`;
                }
                const testResponse = await axios.get(`${geoserverUrl}/ows`, {
                    params,
                    httpsAgent: new (require('https').Agent)({
                        rejectUnauthorized: false
                    }),
                    timeout: 10000
                });

                // Check if this format gives us proper GeoJSON
                if (testResponse.data?.features && Array.isArray(testResponse.data.features)) {
                    console.log(`✅ Format ${format} works! Found ${testResponse.data.features.length} features`);
                    workingParams = params;
                    workingResponse = testResponse;
                    break;
                } else {
                    console.log(` Format ${format} failed - no features array`);
                }

            } catch (formatError) {
                console.log(` Format ${format} error:`, formatError instanceof Error ? formatError.message : 'Unknown error');
            }
        }

        if (!workingResponse) {
            throw new Error('All output formats failed to return proper GeoJSON');
        }

        const params = workingParams;
        const wfsResponse = workingResponse;

        const responseData = wfsResponse.data;

        // Debug: Log raw response info
        console.log('🔍 Raw WFS Response Info:', {
            status: wfsResponse.status,
            contentType: wfsResponse.headers['content-type'],
            dataType: typeof responseData,
            isArray: Array.isArray(responseData),
            dataKeys: Object.keys(responseData || {}).slice(0, 10), // First 10 keys
            dataLength: Array.isArray(responseData) ? responseData.length : 'not array'
        });

        res.json({
            success: true,
            debug: {
                requestParams: params,
                responseStructure: {
                    hasFeatures: !!responseData?.features,
                    featureCount: responseData?.features?.length || 0,
                    responseKeys: Object.keys(responseData || {}),
                    type: responseData?.type,
                    crs: responseData?.crs
                },
                sampleFeatures: responseData?.features?.slice(0, 3).map((feature: any) => ({
                    properties: feature.properties,
                    geometryType: feature.geometry?.type
                })) || [],
                extractionTest: {
                    foundMunicipalities: extractMunicipalitiesFromGeoJSON(responseData).length,
                    fieldAnalysis: responseData?.features?.[0] ? {
                        allFields: Object.keys(responseData.features[0].properties || {}),
                        adm2_en: responseData.features[0].properties?.adm2_en,
                        adm1_en: responseData.features[0].properties?.adm1_en,
                        municipalityFieldCandidates: Object.keys(responseData.features[0].properties || {}).filter((key: string) =>
                            key.toLowerCase().includes('mun') ||
                            key.toLowerCase().includes('adm2') ||
                            key.toLowerCase().includes('municipality')
                        )
                    } : null
                }
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Error in debug municipalities endpoint:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to debug municipalities',
            details: error instanceof Error ? error.message : 'Unknown error',
            axiosError: {
                status: (error as any)?.response?.status,
                statusText: (error as any)?.response?.statusText,
                data: (error as any)?.response?.data
            }
        });
    }
});

/**
 * @swagger
 * /api/ows/administrative-boundaries/wards:
 *   get:
 *     summary: Get wards from the official SA wards layer
 *     tags: [Administrative Boundaries]
 *     description: Get ward-level administrative areas from the official sa_wards2020 layer
 *     parameters:
 *       - in: query
 *         name: municipality
 *         schema:
 *           type: string
 *         description: Filter wards by municipality name or code (cat_b)
 *       - in: query
 *         name: municipalityCode
 *         schema:
 *           type: string
 *         description: Filter wards by municipality code (cat_b field)
 *       - in: query
 *         name: district
 *         schema:
 *           type: string
 *         description: Filter wards by district name or code
 *       - in: query
 *         name: districtCode
 *         schema:
 *           type: string
 *         description: Filter wards by district code (districtco field)
 *       - in: query
 *         name: province
 *         schema:
 *           type: string
 *         description: Filter wards by province name
 *     responses:
 *       200:
 *         description: Wards retrieved successfully
 *       500:
 *         description: Failed to retrieve wards
 */
router.get('/administrative-boundaries/wards', cacheMiddleware(300), async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';
        const layerName = 'geonode:sa_wards2020'; // Official SA wards layer

        const municipality = req.query.municipality as string;
        const municipalityCode = req.query.municipalityCode as string;
        const district = req.query.district as string;
        const districtCode = req.query.districtCode as string;
        const province = req.query.province as string;

        console.log(`🏛️ Loading wards from ${layerName}...`, {
            municipality,
            municipalityCode,
            district,
            districtCode,
            province
        });

        // Build CQL filter based on provided parameters
        const filters = [];

        if (province) {
            filters.push(`province='${province}'`);
        }

        if (districtCode) {
            filters.push(`districtco='${districtCode}'`);
        } else if (district) {
            filters.push(`district='${district}'`);
        }

        if (municipalityCode) {
            filters.push(`cat_b='${municipalityCode}'`);
        } else if (municipality) {
            // Handle municipality name matching (municipali field is truncated)
            filters.push(`municipali LIKE '%${municipality}%'`);
        }

        const params: any = {
            SERVICE: 'WFS',
            REQUEST: 'GetFeature',
            typeName: layerName,
            version: '1.0.0',
            outputFormat: 'application/json',
            maxFeatures: 5000, // ✅ CRITICAL FIX: Increased from 50 to handle all wards
            propertyName: 'province,district,cat_b,municipali,wardid,wardno,wardlabel,districtco' // Only get needed fields
        };

        if (filters.length > 0) {
            params.CQL_FILTER = filters.join(' AND ');
        }

        const wfsResponse = await axios.get(`${geoserverUrl}/ows`, {
            params,
            httpsAgent: new (require('https').Agent)({
                rejectUnauthorized: false
            }),
            timeout: 15000
        });

        const wards = extractWardsFromSALayer(wfsResponse.data);

        console.log(`✅ Successfully extracted ${wards.length} wards from SA wards layer`);

        res.json({
            success: true,
            data: wards,
            source: 'SA_WARDS_2020',
            count: wards.length,
            filteredBy: {
                province,
                district,
                districtCode,
                municipality,
                municipalityCode
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('❌ Failed to load wards from SA wards layer:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve wards from SA wards layer',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});

export const owsRouter = router;
function getLocalMunicipalitiesForDistrict(features: any, districtCode: string): any {
    throw new Error('Function not implemented.');
}

