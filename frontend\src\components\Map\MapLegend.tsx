import React, { useState, useEffect } from 'react';
import { WMSLayer, fetchLayerMetadata, fetchLayerFeatures, fetchLayerMetadataDetailed } from '../../services/geoserverService';
import { LayerDiscovery } from '../../types/discovery';
import './MapLegend.css';
import { Modal, Button, ButtonGroup, Spinner, Alert, Table, Badge, Tooltip, OverlayTrigger, Form } from 'react-bootstrap';
import { API_CONFIG } from '../../config';
import { Maximize2, Info, Clock, Eye, Database, Calendar, Layers, Grid, X, Settings, HelpCircle, Download, Share2, BookOpen } from 'lucide-react';

interface MapLegendPanelProps {
  visibleLayers: WMSLayer[];
  mapHeight?: number;

  layerOpacities?: { [layerName: string]: number };
  onOpacityChange?: (layerName: string, newOpacity: number) => void;
  layerTemporalSelections?: { [layerName: string]: string };
  onTemporalSelectionChange?: (layerName: string, timeSelection: string) => void;

  // Enhanced props for user experience
  userMode?: 'simple' | 'advanced';
  onUserModeChange?: (mode: 'simple' | 'advanced') => void;

  // Legend selection props
  selectedLegendLayer?: string;
  onLegendLayerSelect?: (layerName: string) => void;

  // Panel control
  onClose?: () => void;

  // Auto-trigger info modal
  autoShowInfo?: boolean;
}

const renderMetadata = (data: LayerDiscovery) => (
  <div>
    <h5>{data.title}</h5>
    <p>{data.abstract}</p>

    {/* Basic Information */}
    <div className="mb-3">
      <h6>Basic Information</h6>
      <div><strong>Name:</strong> {data.name}</div>
      {data.attribution && <div><strong>Attribution:</strong> {data.attribution}</div>}
      {data.keywords.length > 0 && (
        <div><strong>Keywords:</strong> {data.keywords.join(', ')}</div>
      )}
      <div><strong>Style:</strong> {data.style || 'N/A'}</div>
      <div><strong>Queryable:</strong> {data.queryable ? 'Yes' : 'No'}</div>
    </div>

    {/* Service Support */}
    <div className="mb-3">
      <h6>Service Support</h6>
      <div>
        {data.supports.WMS && <Badge bg="primary" className="me-1">WMS</Badge>}
        {data.supports.WFS && <Badge bg="success" className="me-1">WFS</Badge>}
        {data.supports.WMTS && <Badge bg="info" className="me-1">WMTS</Badge>}
      </div>
      {data.formats.length > 0 && (
        <div className="mt-2">
          <strong>Formats:</strong>
          {data.formats.map((fmt, idx) => (
            <Badge bg="info" className="me-1" key={fmt + idx}>
              {fmt}
            </Badge>
          ))}
        </div>
      )}
    </div>

    {/* Coordinate Reference Systems */}
    {data.crs && data.crs.length > 0 && (
      <div className="mb-3">
        <h6>Supported CRS</h6>
        <div>{data.crs.join(', ')}</div>
      </div>
    )}

    {/* Bounding Box */}
    {data.bbox && (
      <div className="mb-3">
        <h6>Bounding Box ({data.bbox.SRS || 'Unknown CRS'})</h6>
        <div style={{ fontSize: 13 }}>
          <strong>Min X:</strong> {data.bbox.minx}, <strong>Min Y:</strong> {data.bbox.miny}<br />
          <strong>Max X:</strong> {data.bbox.maxx}, <strong>Max Y:</strong> {data.bbox.maxy}
        </div>
      </div>
    )}

    {/* Temporal Information */}
    {data.temporal && (
      <div className="mb-3">
        <h6><Clock size={16} className="me-1" />Temporal Information</h6>
        <div className="bg-light p-2 rounded">
          <div><strong>Dimension:</strong> {data.temporal.name}</div>
          {data.temporal.units && <div><strong>Units:</strong> {data.temporal.units}</div>}
          {data.temporal.default && <div><strong>Default Time:</strong> {data.temporal.default}</div>}
          {data.temporal.extent && <div><strong>Extent:</strong> {data.temporal.extent}</div>}
          {data.temporal.values && data.temporal.values.length > 0 && (
            <div><strong>Available Times:</strong> {data.temporal.values.length} time instances</div>
          )}
        </div>
      </div>
    )}

    {/* Scale Information */}
    {(data.minScaleDenominator || data.maxScaleDenominator) && (
      <div className="mb-3">
        <h6>Scale Information</h6>
        {data.minScaleDenominator && <div><strong>Min Scale:</strong> 1:{data.minScaleDenominator.toLocaleString()}</div>}
        {data.maxScaleDenominator && <div><strong>Max Scale:</strong> 1:{data.maxScaleDenominator.toLocaleString()}</div>}
      </div>
    )}

    {/* Metadata URLs */}
    {data.metadataUrls && data.metadataUrls.length > 0 && (
      <div className="mb-3">
        <h6>Metadata Links</h6>
        {data.metadataUrls.map((url, index) => (
          <div key={index}>
            <a href={url.onlineResource} target="_blank" rel="noopener noreferrer">
              {url.type} ({url.format})
            </a>
          </div>
        ))}
      </div>
    )}

    {/* Data URLs */}
    {data.dataUrls && data.dataUrls.length > 0 && (
      <div className="mb-3">
        <h6>Data Links</h6>
        {data.dataUrls.map((url, index) => (
          <div key={index}>
            <a href={url.onlineResource} target="_blank" rel="noopener noreferrer">
              Download ({url.format})
            </a>
          </div>
        ))}
      </div>
    )}
  </div>
);

const renderFeatures = (data: any) => {
  if (!data || !Array.isArray(data.features) || data.features.length === 0) {
    return <div>No features found.</div>;
  }
  const columns = Object.keys(data.features[0].properties || {});
  return (
    <div style={{ maxHeight: 350, overflowY: 'auto' }}>
      <Table striped bordered hover size="sm">
        <thead>
          <tr>
            {columns.map(col => <th key={col}>{col}</th>)}
          </tr>
        </thead>
        <tbody>
          {data.features.slice(0, 10).map((feat: any, idx: number) => (
            <tr key={idx}>
              {columns.map(col => <td key={col}>{String(feat.properties[col])}</td>)}
            </tr>
          ))}
        </tbody>
      </Table>
      <div style={{ fontSize: 12, color: '#888' }}>
        Showing {Math.min(10, data.features.length)} of {data.features.length} features.
      </div>
    </div>
  );
};

const MapLegendPanel: React.FC<MapLegendPanelProps> = ({
  visibleLayers,
  layerOpacities = {},
  onOpacityChange,
  layerTemporalSelections = {},
  onTemporalSelectionChange,
  userMode = 'simple',
  onUserModeChange,
  selectedLegendLayer,
  onLegendLayerSelect,
  onClose,
  autoShowInfo = false
}) => {
  const [activeModal, setActiveModal] = useState<null | 'metadata' | 'features' | 'help'>(null);
  const [activeLayer, setActiveLayer] = useState<WMSLayer | null>(null);
  const [modalData, setModalData] = useState<LayerDiscovery | any | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(true); // Always start expanded

  // Auto-trigger info modal when autoShowInfo is true and a layer is selected
  useEffect(() => {
    if (autoShowInfo && selectedLegendLayer && visibleLayers.length > 0) {
      const selectedLayer = visibleLayers.find(layer => layer.name === selectedLegendLayer);
      if (selectedLayer) {
        // Automatically open the metadata modal for the selected layer
        handleOpenModal('metadata', selectedLayer);
      }
    }
  }, [autoShowInfo, selectedLegendLayer, visibleLayers]);

  // Handle layer card selection
  const handleLayerCardClick = (layer: WMSLayer) => {
    if (onLegendLayerSelect) {
      onLegendLayerSelect(layer.name);
    }
  };

  const handleOpenModal = async (type: 'metadata' | 'features', layer: WMSLayer) => {
    setActiveModal(type);
    setActiveLayer(layer);
    setLoading(true);
    setError(null);
    setModalData(null);
    try {
      let data = null;
      if (type === 'metadata') {
        // Use the detailed metadata endpoint for comprehensive OGC metadata
        data = await fetchLayerMetadataDetailed(layer.name);
      } else if (type === 'features') {
        data = await fetchLayerFeatures(layer.name);
      }
      setModalData(data);
    } catch (err: any) {
      setError(err?.message || 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };



  return (
    <div className={`map-legend-panel expanded user-mode-${userMode}`}>
      {/* Always show expanded panel */}
          <div className="legend-header">
            <div className="legend-header-content">
              <Layers size={18} className="me-2 header-icon" />
              <h4 className="legend-title mb-0">
                Layer Information
                <Badge bg="secondary" className="ms-2">
                  {visibleLayers.length}
                </Badge>
              </h4>

              {/* User Mode Toggle */}
              <div className="user-mode-toggle ms-3">
                <ButtonGroup size="sm">
                  <OverlayTrigger
                    placement="bottom"
                    overlay={<Tooltip>Simple view for general users</Tooltip>}
                  >
                    <Button
                      variant={userMode === 'simple' ? 'light' : 'outline-light'}
                      onClick={() => onUserModeChange?.('simple')}
                      className="mode-btn"
                    >
                      <BookOpen size={12} className="me-1" />
                      Simple
                    </Button>
                  </OverlayTrigger>
                  <OverlayTrigger
                    placement="bottom"
                    overlay={<Tooltip>Advanced view with technical details</Tooltip>}
                  >
                    <Button
                      variant={userMode === 'advanced' ? 'light' : 'outline-light'}
                      onClick={() => onUserModeChange?.('advanced')}
                      className="mode-btn"
                    >
                      <Settings size={12} className="me-1" />
                      Advanced
                    </Button>
                  </OverlayTrigger>
                </ButtonGroup>
              </div>
            </div>

            <div className="legend-header-actions">
              {/* Help Button */}
              <OverlayTrigger
                placement="bottom"
                overlay={<Tooltip>Get help using the legend panel</Tooltip>}
              >
                <Button
                  variant="outline-light"
                  size="sm"
                  className="me-2"
                  onClick={() => setActiveModal('help')}
                >
                  <HelpCircle size={14} />
                </Button>
              </OverlayTrigger>

              <Button
                variant="outline-light"
                size="sm"
                className="legend-minimize-btn"
                onClick={() => onClose?.()}
                title="Close legend panel"
              >
                <X size={16} />
              </Button>
            </div>
          </div>

          <div className="legend-content">
        {visibleLayers && visibleLayers.length > 0 ? (
          visibleLayers.map((layer) => {
            const isSelected = selectedLegendLayer === layer.name;
            return (
              <div
                key={layer.name}
                className={`legend-item-card ${isSelected ? 'selected' : ''}`}
                onClick={() => handleLayerCardClick(layer)}
                style={{ cursor: 'pointer' }}
                title="Click to view this layer's legend"
              >
                {/* Color-coded accent bar */}
                <div className={`layer-accent-bar ${isSelected ? 'selected' : ''}`} />

              <div className="legend-item-header">
                <div className="layer-title-section">
                  <div className="layer-icon">
                    <Layers size={16} />
                  </div>
                  <div className="layer-title-container">
                    <OverlayTrigger
                      placement="top"
                      overlay={<Tooltip>{layer.title || layer.name}</Tooltip>}
                    >
                      <h6 className="layer-title">
                        {(layer.title || layer.name).length > 25
                          ? `${(layer.title || layer.name).substring(0, 25)}...`
                          : (layer.title || layer.name)
                        }
                        {isSelected && (
                          <Badge bg="primary" className="ms-2 selected-badge">
                            <Eye size={10} className="me-1" />
                            Legend Shown
                          </Badge>
                        )}
                      </h6>
                    </OverlayTrigger>

                    {/* Layer badges - show different info based on user mode */}
                    <div className="layer-badges">
                      {userMode === 'simple' ? (
                        // Simple mode: Show user-friendly badges
                        <>
                          {(layer as any).queryable && (
                            <Badge bg="success" className="layer-badge">
                              <Eye size={10} /> Clickable
                            </Badge>
                          )}
                          {((layer as any).isTemporal || (layer as any).temporal?.hasTemporal) && (
                            <Badge bg="primary" className="layer-badge">
                              <Calendar size={10} /> Time-based
                            </Badge>
                          )}
                        </>
                      ) : (
                        // Advanced mode: Show technical badges
                        <>
                          {(layer as any).queryable && (
                            <Badge bg="info" className="layer-badge">
                              <Eye size={10} /> Queryable
                            </Badge>
                          )}
                          {((layer as any).isTemporal || (layer as any).temporal?.hasTemporal) && (
                            <Badge bg="warning" className="layer-badge">
                              <Calendar size={10} /> Temporal
                            </Badge>
                          )}
                          {(layer as any).supports?.WFS && (
                            <Badge bg="success" className="layer-badge">
                              <Database size={10} /> WFS
                            </Badge>
                          )}
                          {(layer as any).supports?.WMTS && (
                            <Badge bg="secondary" className="layer-badge">
                              <Grid size={10} /> WMTS
                            </Badge>
                          )}
                        </>
                      )}
                    </div>
                  </div>

                  {/* Simple mode: Show layer description */}
                  {userMode === 'simple' && (layer as any).abstract && (
                    <div className="layer-description mt-2">
                      <small className="text-muted">
                        {(layer as any).abstract.length > 100
                          ? `${(layer as any).abstract.substring(0, 100)}...`
                          : (layer as any).abstract
                        }
                      </small>
                    </div>
                  )}
                </div>

                <OverlayTrigger
                  placement="top"
                  overlay={<Tooltip>View detailed layer information</Tooltip>}
                >
                  <Button
                    variant="outline-secondary"
                    size="sm"
                    onClick={() => handleOpenModal('metadata', layer)}
                    className="info-btn"
                  >
                    <Info size={14} />
                  </Button>
                </OverlayTrigger>
              </div>

              {/* Enhanced Legend image with better scaling */}
              <div className="legend-image-section">
                <div className="legend-image-header">
                  <span className="legend-section-title">Legend</span>

                </div>

                <div className="legend-image-wrapper-enhanced">
                  <img
                    src={`${API_CONFIG.BASE_URL}/ows/wms-proxy?SERVICE=WMS&REQUEST=GetLegendGraphic&LAYER=${layer.name}&FORMAT=image/png&VERSION=1.1.1`}
                    alt={`Legend for ${layer.name}`}
                    className="legend-image-enhanced"

                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.onerror = null;
                      target.style.display = 'none';
                      const fallbackDiv = document.createElement('div');
                      fallbackDiv.className = 'legend-fallback';
                      fallbackDiv.innerHTML = `
                        <div class="fallback-icon">📊</div>
                        <div class="fallback-text">No legend available</div>
                      `;
                      target.parentElement?.appendChild(fallbackDiv);
                    }}
                  />
                </div>
              </div>

                {/* Enhanced Opacity Control */}
                {onOpacityChange && (
                  <div className="opacity-control-enhanced">
                    <div className="opacity-header">
                      <label htmlFor={`opacity-${layer.name}`} className="opacity-label">
                        <Eye size={12} /> Opacity
                      </label>
                      <OverlayTrigger
                        placement="top"
                        overlay={<Tooltip>Current opacity: {Math.round((layerOpacities[layer.name] ?? 1) * 100)}%</Tooltip>}
                      >
                        <span className="opacity-value">
                          {Math.round((layerOpacities[layer.name] ?? 1) * 100)}%
                        </span>
                      </OverlayTrigger>
                    </div>
                    <div className="opacity-slider-container">
                      <input
                        id={`opacity-${layer.name}`}
                        type="range"
                        min="0"
                        max="100"
                        step="5"
                        value={Math.round((layerOpacities[layer.name] ?? 1) * 100)}
                        onChange={(e) => onOpacityChange(layer.name, Number(e.target.value) / 100)}
                        className="opacity-slider-enhanced"
                        aria-label={`Opacity control for ${layer.title || layer.name}`}
                      />
                      <div className="opacity-marks">
                        <span>0%</span>
                        <span>50%</span>
                        <span>100%</span>
                      </div>
                    </div>
                  </div>
                )}

              {/* Temporal Date Selector */}
              {((layer as any).isTemporal || (layer as any).temporal?.hasTemporal) && (
                <div className="temporal-control mt-2">
                </div>
              )}

              <div className="legend-item-actions">
                <div className="action-buttons-container">
                  {userMode === 'simple' ? (
                    // Simple mode: Show user-friendly actions
                    <>
                      <OverlayTrigger
                        placement="top"
                        overlay={<Tooltip>Learn more about this layer</Tooltip>}
                      >
                        <Button
                          variant="outline-primary"
                          size="sm"
                          onClick={() => handleOpenModal('metadata', layer)}
                          className="action-btn metadata-btn"
                          disabled={loading && activeLayer?.name === layer.name && activeModal === 'metadata'}
                        >
                          {loading && activeLayer?.name === layer.name && activeModal === 'metadata' ? (
                            <Spinner size="sm" animation="border" />
                          ) : (
                            <Info size={12} />
                          )}
                          <span>About</span>
                        </Button>
                      </OverlayTrigger>

                      {(layer as any).queryable && (
                        <OverlayTrigger
                          placement="top"
                          overlay={<Tooltip>View data in this layer</Tooltip>}
                        >
                          <Button
                            variant="outline-success"
                            size="sm"
                            onClick={() => handleOpenModal('features', layer)}
                            className="action-btn features-btn"
                            disabled={loading && activeLayer?.name === layer.name && activeModal === 'features'}
                          >
                            {loading && activeLayer?.name === layer.name && activeModal === 'features' ? (
                              <Spinner size="sm" animation="border" />
                            ) : (
                              <Eye size={12} />
                            )}
                            <span>View Data</span>
                          </Button>
                        </OverlayTrigger>
                      )}
                    </>
                  ) : (
                    // Advanced mode: Show technical actions
                    <>
                      <OverlayTrigger
                        placement="top"
                        overlay={<Tooltip>View layer metadata and properties</Tooltip>}
                      >
                        <Button
                          variant="outline-primary"
                          size="sm"
                          onClick={() => handleOpenModal('metadata', layer)}
                          className="action-btn metadata-btn"
                          disabled={loading && activeLayer?.name === layer.name && activeModal === 'metadata'}
                        >
                          {loading && activeLayer?.name === layer.name && activeModal === 'metadata' ? (
                            <Spinner size="sm" animation="border" />
                          ) : (
                            <Database size={12} />
                          )}
                          <span>Metadata</span>
                        </Button>
                      </OverlayTrigger>

                      <OverlayTrigger
                        placement="top"
                        overlay={<Tooltip>Browse layer features and data</Tooltip>}
                      >
                        <Button
                          variant="outline-success"
                          size="sm"
                          onClick={() => handleOpenModal('features', layer)}
                          className="action-btn features-btn"
                          disabled={loading && activeLayer?.name === layer.name && activeModal === 'features'}
                        >
                          {loading && activeLayer?.name === layer.name && activeModal === 'features' ? (
                            <Spinner size="sm" animation="border" />
                          ) : (
                            <Grid size={12} />
                          )}
                          <span>Features</span>
                        </Button>
                      </OverlayTrigger>

                      {/* Advanced actions */}
                      <OverlayTrigger
                        placement="top"
                        overlay={<Tooltip>Download layer data</Tooltip>}
                      >
                        <Button
                          variant="outline-secondary"
                          size="sm"
                          onClick={() => {
                            const downloadUrl = `${API_CONFIG.BASE_URL}/ows/wfs-proxy?service=WFS&request=GetFeature&typeName=${layer.name}&outputFormat=SHAPE-ZIP`;
                            window.open(downloadUrl, '_blank');
                          }}
                          className="action-btn download-btn"
                        >
                          <Download size={12} />
                          <span>Download</span>
                        </Button>
                      </OverlayTrigger>

                      <OverlayTrigger
                        placement="top"
                        overlay={<Tooltip>Share layer link</Tooltip>}
                      >
                        <Button
                          variant="outline-info"
                          size="sm"
                          onClick={() => {
                            const shareUrl = `${window.location.origin}${window.location.pathname}?layer=${layer.name}`;
                            navigator.clipboard.writeText(shareUrl);
                            // Could add a toast notification here
                          }}
                          className="action-btn share-btn"
                        >
                          <Share2 size={12} />
                          <span>Share</span>
                        </Button>
                      </OverlayTrigger>
                    </>
                  )}
                </div>
              </div>
            </div>
            );
          })
        ) : (
          <p className="text-muted">No layers currently visible</p>
        )}
          </div>

      <Modal show={activeModal !== null} onHide={() => setActiveModal(null)} size="lg">
        <Modal.Header closeButton className="app-header-blue">
          <Modal.Title>
            {activeModal === 'help' ? (
              <>
                <HelpCircle size={20} className="me-2 header-icon" />
                Legend Panel Help
              </>
            ) : (
              <>
                <Database size={20} className="me-2 header-icon" />
                {activeModal === 'metadata' ? 'Layer Metadata' : 'Layer Features'}
                {activeLayer && <small className="ms-2 opacity-75">{activeLayer.title}</small>}
              </>
            )}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {loading && <div className="text-center p-4"><Spinner animation="border" /></div>}
          {error && <Alert variant="danger">{error}</Alert>}
          {!loading && !error && modalData && activeModal !== 'help' && (
            activeModal === 'metadata'
              ? renderMetadata(modalData)
              : renderFeatures(modalData)
          )}
          {activeModal === 'help' && (
            <div className="help-content">
              <h5>Legend Panel Help</h5>

              <div className="mb-4">
                <h6>🎯 User Modes</h6>
                <ul>
                  <li><strong>Simple Mode:</strong> User-friendly interface for general users with basic controls and clear language</li>
                  <li><strong>Advanced Mode:</strong> Technical interface with detailed metadata, download options, and sharing features</li>
                </ul>
              </div>

              <div className="mb-4">
                <h6>🎨 Layer Controls</h6>
                <ul>
                  <li><strong>Opacity Slider:</strong> Adjust layer transparency (0% = invisible, 100% = opaque)</li>
                  <li><strong>Legend Image:</strong> Shows color coding and symbols used in the layer</li>
                  <li><strong>Badges:</strong> Indicate layer capabilities (Clickable/Queryable, Time-based/Temporal)</li>
                </ul>
              </div>

              <div className="mb-4">
                <h6>🔧 Actions Available</h6>
                <div className="row">
                  <div className="col-md-6">
                    <strong>Simple Mode:</strong>
                    <ul>
                      <li><strong>About:</strong> Learn about the layer</li>
                      <li><strong>View Data:</strong> See data in the layer</li>
                    </ul>
                  </div>
                  <div className="col-md-6">
                    <strong>Advanced Mode:</strong>
                    <ul>
                      <li><strong>Metadata:</strong> Technical layer information</li>
                      <li><strong>Features:</strong> Browse layer data table</li>
                      <li><strong>Download:</strong> Get layer data as files</li>
                      <li><strong>Share:</strong> Copy layer link to clipboard</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="mb-4">
                <h6>💡 Tips</h6>
                <ul>
                  <li>Click on the map to get information about clickable layers</li>
                  <li>Use opacity controls to see through layers and compare data</li>
                  <li>Switch between Simple and Advanced modes based on your needs</li>
                  <li>Hover over buttons and controls for helpful tooltips</li>
                </ul>
              </div>
            </div>
          )}
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default MapLegendPanel;
