import axios from 'axios';
import { API_CONFIG } from '../config';

export interface AdministrativeRegion {
  id: string;
  name: string;
  code?: string;
  properties?: Record<string, any>;
}

interface DistrictInfo {
  id: string;
  name: string;
  code: string;
  type: 'district_municipality' | 'metropolitan_municipality';
  municipalities: string[];
  isMetro: boolean;
}

interface WardInfo {
  id: string;
  name: string;
  tlc: string;
  coordinates: [number, number];
  properties?: Record<string, any>;
}

export interface AdministrativeBoundaryData {
  provinces: AdministrativeRegion[];
  districts: AdministrativeRegion[];
  municipalities: AdministrativeRegion[];
  wards: AdministrativeRegion[];
}

// Cache for administrative boundary data
const boundaryCache = new Map<string, any>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Load provinces from the provincial boundaries layer
 */
export const loadProvinces = async (): Promise<AdministrativeRegion[]> => {
  const cacheKey = 'provinces';
  const cached = boundaryCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    console.log('Using cached provinces data');
    return cached.data;
  }

  try {
    // Use GeoNode WFS endpoint directly with a small maxFeatures value (there are only 9 provinces)
    const url = 'https://*************/geoserver/geonode/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=geonode%3Asouth_africa_provincial_boundaries&maxFeatures=20&outputFormat=application%2Fjson';
    console.log('Loading provinces from GeoNode WFS endpoint...', url);
    const response = await axios.get(url, { 
      timeout: 30000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });
    const geojson = response.data;
    if (geojson && Array.isArray(geojson.features)) {
      const provinces: AdministrativeRegion[] = geojson.features.map((feature: any) => ({
        id: feature.properties.adm1_id, // e.g., "EC"
        name: feature.properties.adm1_en, // e.g., "Eastern Cape"
        code: feature.properties.adm1_pcode, // e.g., "ZA2"
        properties: feature.properties
      }));
      
      console.log(`✅ Successfully loaded ${provinces.length} provinces`);
      
      // Sort provinces alphabetically by name for better user experience
      provinces.sort((a, b) => a.name.localeCompare(b.name));
      
      // Cache the result
      boundaryCache.set(cacheKey, {
        data: provinces,
        timestamp: Date.now()
      });
      return provinces;
    } else {
      throw new Error('GeoNode WFS did not return a valid FeatureCollection');
    }
  } catch (error) {
    console.error('Failed to load provinces from GeoNode WFS:', error);
    console.error('Error details:', { 
      message: error instanceof Error ? error.message : 'Unknown error', 
      url: 'https://*************/geoserver/geonode/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=geonode%3Asouth_africa_provincial_boundaries'
    });
    
    // Return hard-coded provinces data as a fallback
    console.warn('Using fallback provinces data due to API error');
    const fallbackProvinces: AdministrativeRegion[] = [
      { id: 'EC', name: 'Eastern Cape', code: 'ZA2' },
      { id: 'FS', name: 'Free State', code: 'ZA3' },
      { id: 'GT', name: 'Gauteng', code: 'ZA4' },
      { id: 'KZN', name: 'KwaZulu-Natal', code: 'ZA5' },
      { id: 'LP', name: 'Limpopo', code: 'ZA6' },
      { id: 'MP', name: 'Mpumalanga', code: 'ZA7' },
      { id: 'NC', name: 'Northern Cape', code: 'ZA8' },
      { id: 'NW', name: 'North West', code: 'ZA9' },
      { id: 'WC', name: 'Western Cape', code: 'ZA10' }
    ];
    
    // Cache the fallback result
    boundaryCache.set(cacheKey, {
      data: fallbackProvinces,
      timestamp: Date.now()
    });
    
    return fallbackProvinces;
  }
};

/**
 * Load districts using the uiengine cascading endpoint
 */
export const loadDistricts = async (provinceId?: string): Promise<DistrictInfo[]> => {
  const cacheKey = `districts_${provinceId || 'all'}`;
  const cached = boundaryCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    console.log('Using cached districts data for province:', provinceId || 'all');
    return cached.data;
  }

  try {
    // Use municipal boundaries GeoJSON to derive districts with server-side filtering
    // South Africa has less than 60 districts and metros combined, so this is more than enough
    // To be extra safe, we'll use a very large maxFeatures value (999) that should get all districts
    // This ensures that:
    //   1. We capture all 8 metros across South Africa
    //   2. We capture all ~44 district municipalities 
    //   3. We have room to handle any future administrative boundary changes
    // Even with this high value, the response size is manageable since:
    //   1. We apply CQL filtering by province when specified
    //   2. The total number of districts is small regardless
    let url = 'https://*************/geoserver/geonode/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=geonode%3Asouth_africa_municipal_boundaries&maxFeatures=999&outputFormat=application%2Fjson';
    
    // Add CQL filter for provinceId if provided
    if (provinceId) {
      url += `&CQL_FILTER=${encodeURIComponent(`adm1_id='${provinceId}'`)}`;
    }
    
    console.log('Loading districts from municipal GeoJSON...', url);
    const response = await axios.get(url, { 
      timeout: 30000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });
    const geojson = response.data;
    if (geojson && Array.isArray(geojson.features)) {
      // Group by adm2_id, only keep those where adm2_id starts with 'DC' (district municipalities)
      // Also include metros (adm2_id not starting with 'DC') as isMetro: true
      const districtMap: Record<string, DistrictInfo> = {};
      geojson.features.forEach((feature: any) => {
        const adm2_id = feature.properties.adm2_id;
        const adm2_en = feature.properties.adm2_en;
        const adm1_id = feature.properties.adm1_id;
        const code = feature.properties.adm2_pcode;
        // If provinceId is set, filter by province
        if (provinceId && adm1_id !== provinceId) return;
        if (adm2_id && adm2_en) {
          if (adm2_id.startsWith('DC')) {
            // District municipality
            if (!districtMap[adm2_id]) {
              districtMap[adm2_id] = {
                id: adm2_id,
                name: adm2_en,
                code: code,
                type: 'district_municipality',
                municipalities: [],
                isMetro: false
              };
            }
          } else {
            // Metro (not starting with DC)
            if (!districtMap[adm2_id]) {
              districtMap[adm2_id] = {
                id: adm2_id,
                name: adm2_en,
                code: code,
                type: 'metropolitan_municipality',
                municipalities: [],
                isMetro: true
              };
            }
          }
        }
      });
      const districts: DistrictInfo[] = Object.values(districtMap);
      
      console.log(`✅ Successfully loaded ${districts.length} districts for province: ${provinceId || 'all'}`);
      
      // Sort districts alphabetically with metros first, then districts
      districts.sort((a, b) => {
        if (a.isMetro && !b.isMetro) return -1;
        if (!a.isMetro && b.isMetro) return 1;
        return a.name.localeCompare(b.name);
      });
      
      // Cache the result
      boundaryCache.set(cacheKey, {
        data: districts,
        timestamp: Date.now()
      });
      return districts;
    } else {
      throw new Error('GeoNode WFS did not return a valid FeatureCollection');
    }
  } catch (error) {
    console.error('Failed to load districts from municipal GeoJSON:', error);
    console.error('Error details:', { 
      message: error instanceof Error ? error.message : 'Unknown error', 
      province: provinceId || 'all',
      urlPrefix: 'https://*************/geoserver/geonode/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=geonode%3Asouth_africa_municipal_boundaries'
    });
    
    // Return hard-coded fallback districts for the selected province
    console.warn('Using fallback districts data due to API error');
    let fallbackDistricts: DistrictInfo[] = [];
    
    // Generate fallback data based on province (simplified)
    if (provinceId === 'GT') {
      // Gauteng
      fallbackDistricts = [
        { id: 'JHB', name: 'City of Johannesburg', code: 'JHB', type: 'metropolitan_municipality', municipalities: [], isMetro: true },
        { id: 'TSH', name: 'City of Tshwane', code: 'TSH', type: 'metropolitan_municipality', municipalities: [], isMetro: true },
        { id: 'EKU', name: 'Ekurhuleni', code: 'EKU', type: 'metropolitan_municipality', municipalities: [], isMetro: true },
        { id: 'DC42', name: 'Sedibeng', code: 'DC42', type: 'district_municipality', municipalities: [], isMetro: false },
        { id: 'DC48', name: 'West Rand', code: 'DC48', type: 'district_municipality', municipalities: [], isMetro: false }
      ];
    } else if (provinceId === 'WC') {
      // Western Cape
      fallbackDistricts = [
        { id: 'CPT', name: 'City of Cape Town', code: 'CPT', type: 'metropolitan_municipality', municipalities: [], isMetro: true },
        { id: 'DC1', name: 'West Coast', code: 'DC1', type: 'district_municipality', municipalities: [], isMetro: false },
        { id: 'DC2', name: 'Cape Winelands', code: 'DC2', type: 'district_municipality', municipalities: [], isMetro: false },
        { id: 'DC3', name: 'Overberg', code: 'DC3', type: 'district_municipality', municipalities: [], isMetro: false },
        { id: 'DC4', name: 'Garden Route', code: 'DC4', type: 'district_municipality', municipalities: [], isMetro: false },
        { id: 'DC5', name: 'Central Karoo', code: 'DC5', type: 'district_municipality', municipalities: [], isMetro: false }
      ];
    } else if (provinceId) {
      // Province-specific generic fallback with reasonable examples
      const prefix = provinceId;
      fallbackDistricts = [
        { id: `${prefix}_METRO1`, name: `${provinceId} Metropolitan Municipality`, code: `${prefix}METRO`, type: 'metropolitan_municipality', municipalities: [], isMetro: true },
        { id: `${prefix}_DC1`, name: `${provinceId} Northern District`, code: `${prefix}DC1`, type: 'district_municipality', municipalities: [], isMetro: false },
        { id: `${prefix}_DC2`, name: `${provinceId} Southern District`, code: `${prefix}DC2`, type: 'district_municipality', municipalities: [], isMetro: false },
        { id: `${prefix}_DC3`, name: `${provinceId} Eastern District`, code: `${prefix}DC3`, type: 'district_municipality', municipalities: [], isMetro: false },
        { id: `${prefix}_DC4`, name: `${provinceId} Western District`, code: `${prefix}DC4`, type: 'district_municipality', municipalities: [], isMetro: false }
      ];
    } else {
      // Generic fallback for other provinces
      fallbackDistricts = [
        { id: 'GENERIC_METRO1', name: 'Metropolitan Municipality 1', code: 'METRO1', type: 'metropolitan_municipality', municipalities: [], isMetro: true },
        { id: 'GENERIC_METRO2', name: 'Metropolitan Municipality 2', code: 'METRO2', type: 'metropolitan_municipality', municipalities: [], isMetro: true },
        { id: 'GENERIC_DC1', name: 'Northern District Municipality', code: 'DC1', type: 'district_municipality', municipalities: [], isMetro: false },
        { id: 'GENERIC_DC2', name: 'Southern District Municipality', code: 'DC2', type: 'district_municipality', municipalities: [], isMetro: false },
        { id: 'GENERIC_DC3', name: 'Eastern District Municipality', code: 'DC3', type: 'district_municipality', municipalities: [], isMetro: false }
      ];
    }
    
    // Cache the fallback result
    boundaryCache.set(cacheKey, {
      data: fallbackDistricts,
      timestamp: Date.now()
    });
    
    return fallbackDistricts;
  }
};

/**
 * Load municipalities for a specific province and/or district
 */
export const loadMunicipalities = async (provinceName?: string, districtCode?: string): Promise<AdministrativeRegion[]> => {
  const cacheKey = `municipalities-${provinceName || 'all'}-${districtCode || 'all'}`;
  const cached = boundaryCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    console.log('Using cached municipalities data for province:', provinceName, 'district:', districtCode);
    return cached.data;
  }

  try {
    // Use GeoNode WFS endpoint with server-side filtering (CQL)
    // Using a large maxFeatures value to ensure we get all municipalities for a province/district
    // We use a high value (999) for these reasons:
    //   1. A province like KwaZulu-Natal has around 50-60 municipalities
    //   2. Districts typically have 5-10 local municipalities each
    //   3. We need to ensure all municipalities are loaded for proper selection
    // With CQL filtering applied, the actual number of features returned will be much lower
    // This high value ensures complete data, especially for large provinces
    let url = 'https://*************/geoserver/geonode/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=geonode%3Asouth_africa_municipal_boundaries&maxFeatures=999&outputFormat=application%2Fjson';
    
    // Build CQL filter for server-side filtering
    const filters = [];
    if (provinceName) {
      filters.push(`adm1_id='${provinceName}'`);
    }
    if (districtCode) {
      filters.push(`district='${districtCode}'`);
    }
    
    if (filters.length > 0) {
      url += `&CQL_FILTER=${encodeURIComponent(filters.join(' AND '))}`;
    }
    
    console.log('Loading municipalities from GeoNode WFS endpoint with CQL filter...', url);
    const response = await axios.get(url, { 
      timeout: 30000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      } 
    });
    const geojson = response.data;
    if (geojson && Array.isArray(geojson.features)) {
      let municipalities: AdministrativeRegion[] = geojson.features.map((feature: any) => ({
        id: feature.properties.adm2_id, // e.g., "DC44"
        name: feature.properties.adm2_en, // e.g., "Alfred Nzo"
        code: feature.properties.adm2_pcode, // e.g., "ZA244"
        properties: feature.properties
      }));
      
      console.log(`✅ Successfully loaded ${municipalities.length} municipalities using server-side CQL filtering`);
      
      // Sort alphabetically by name for better user experience
      municipalities.sort((a, b) => a.name.localeCompare(b.name));
      
      // Cache the result
      boundaryCache.set(cacheKey, {
        data: municipalities,
        timestamp: Date.now()
      });
      return municipalities;
    } else {
      throw new Error('GeoNode WFS did not return a valid FeatureCollection');
    }
  } catch (error) {
    console.error('Failed to load municipalities from GeoNode WFS:', error);
    console.error('Error details:', { 
      message: error instanceof Error ? error.message : 'Unknown error', 
      province: provinceName || 'all',
      district: districtCode || 'all',
      filterCount: (provinceName ? 1 : 0) + (districtCode ? 1 : 0),
      urlPrefix: 'https://*************/geoserver/geonode/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=geonode%3Asouth_africa_municipal_boundaries'
    });
    
    // Return hard-coded fallback municipalities for the selected province/district
    console.warn('Using fallback municipalities data due to API error');
    let fallbackMunicipalities: AdministrativeRegion[] = [];
    
    // Generate some fallback data (simplified)
    if (provinceName === 'GT') {
      if (districtCode === 'DC42') { // Sedibeng
        fallbackMunicipalities = [
          { id: 'GT421', name: 'Emfuleni', code: 'GT421' },
          { id: 'GT422', name: 'Midvaal', code: 'GT422' },
          { id: 'GT423', name: 'Lesedi', code: 'GT423' }
        ];
      } else {
        fallbackMunicipalities = [
          { id: 'GT_MUNI1', name: 'Local Municipality 1', code: 'GTLM1' },
          { id: 'GT_MUNI2', name: 'Local Municipality 2', code: 'GTLM2' }
        ];
      }
    } else if (provinceName === 'WC') {
      if (districtCode === 'DC1') { // West Coast
        fallbackMunicipalities = [
          { id: 'WC011', name: 'Matzikama', code: 'WC011' },
          { id: 'WC012', name: 'Cederberg', code: 'WC012' },
          { id: 'WC013', name: 'Bergrivier', code: 'WC013' },
          { id: 'WC014', name: 'Saldanha Bay', code: 'WC014' },
          { id: 'WC015', name: 'Swartland', code: 'WC015' }
        ];
      } else {
        fallbackMunicipalities = [
          { id: 'WC_MUNI1', name: 'Local Municipality 1', code: 'WCLM1' },
          { id: 'WC_MUNI2', name: 'Local Municipality 2', code: 'WCLM2' }
        ];
      }
    } else if (provinceName) {
      // Create province-specific example municipalities
      const provincePrefix = provinceName;
      fallbackMunicipalities = [
        { id: `${provincePrefix}M1`, name: `${provinceName} Local Municipality 1`, code: `${provincePrefix}LM1` },
        { id: `${provincePrefix}M2`, name: `${provinceName} Local Municipality 2`, code: `${provincePrefix}LM2` },
        { id: `${provincePrefix}M3`, name: `${provinceName} Local Municipality 3`, code: `${provincePrefix}LM3` },
        { id: `${provincePrefix}M4`, name: `${provinceName} Local Municipality 4`, code: `${provincePrefix}LM4` },
        { id: `${provincePrefix}M5`, name: `${provinceName} Local Municipality 5`, code: `${provincePrefix}LM5` }
      ];
    } else if (districtCode) {
      // Create district-specific example municipalities
      const districtPrefix = districtCode;
      fallbackMunicipalities = [
        { id: `${districtPrefix}M1`, name: `${districtCode} Local Municipality 1`, code: `${districtPrefix}LM1` },
        { id: `${districtPrefix}M2`, name: `${districtCode} Local Municipality 2`, code: `${districtPrefix}LM2` },
        { id: `${districtPrefix}M3`, name: `${districtCode} Local Municipality 3`, code: `${districtPrefix}LM3` }
      ];
    } else {
      // Generic fallback for other provinces/districts
      fallbackMunicipalities = [
        { id: 'MUNI1', name: 'Local Municipality 1', code: 'LM1' },
        { id: 'MUNI2', name: 'Local Municipality 2', code: 'LM2' },
        { id: 'MUNI3', name: 'Local Municipality 3', code: 'LM3' },
        { id: 'MUNI4', name: 'Local Municipality 4', code: 'LM4' },
        { id: 'MUNI5', name: 'Local Municipality 5', code: 'LM5' }
      ];
    }
    
    // Cache the fallback result
    boundaryCache.set(cacheKey, {
      data: fallbackMunicipalities,
      timestamp: Date.now()
    });
    
    return fallbackMunicipalities;
  }
};

/**
 * Load wards using the official SA wards 2020 layer
 */
export const loadWards = async (
  municipalityName?: string,
  municipalityCode?: string,
  districtCode?: string,
  province?: string
): Promise<WardInfo[]> => {
  const cacheKey = `wards_${municipalityCode || municipalityName || 'all'}_${districtCode || 'all'}_${province || 'all'}`;
  const cached = boundaryCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }

  try {
    // Use GeoNode WFS endpoint with proper CQL filtering and appropriate maxFeatures
    // We need a larger value for wards as municipalities can have many wards (40-100 each)
    // We use a high value (999) since we'll be applying specific filtering:
    //   1. By municipality code - typically returns 20-100 wards
    //   2. By district code - typically returns 50-200 wards 
    //   3. By province - typically returns 100-300 wards with filtering
    // The server-side filtering will dramatically reduce the actual data returned
    // This high value ensures we don't miss any wards in large municipalities
    let url = 'https://*************/geoserver/geonode/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=geonode%3Asa_wards2020&maxFeatures=999&outputFormat=application%2Fjson';

    // Build CQL filter for server-side filtering (much more efficient)
    // At least one filter is required to avoid trying to load all wards (which is too many)
    const filters = [];
    
    if (municipalityCode) {
      // Most specific filter - by municipality code
      filters.push(`cat_b='${municipalityCode}'`);
    } else if (districtCode) {
      // Filter by district if municipality not provided
      filters.push(`districtco='${districtCode}'`);
    } else if (province) {
      // Filter by province if no district or municipality
      filters.push(`province='${province}'`);
    } else {
      // Prevent loading all wards - require at least province
      console.warn('No filtering criteria provided for wards - adding limit to prevent excessive data load');
      filters.push(`wardno < 10`); // Just get a few wards as an example
    }

    url += `&CQL_FILTER=${encodeURIComponent(filters.join(' AND '))}`;
    
    console.log('Loading wards from GeoNode WFS endpoint with CQL filter...', url);
    console.log('Filters applied:', filters);
    
    const response = await axios.get(url, { 
      timeout: 30000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });
    const geojson = response.data;
    if (geojson && Array.isArray(geojson.features)) {
      const wards: WardInfo[] = geojson.features.map((feature: any) => ({
        id: feature.properties.wardid, // e.g., "10503001"
        name: `Ward ${feature.properties.wardno} (${feature.properties.wardlabel})`, // e.g., "Ward 1 (WC053_1)"
        tlc: feature.properties.municipali || feature.properties.cat_b, // Municipality name or code
        coordinates: feature.geometry?.coordinates?.[0]?.[0]?.[0] || [0, 0],
        properties: feature.properties
      }));

      // Server-side filtering via CQL is much more efficient than client-side filtering
      console.log(`✅ Successfully loaded ${wards.length} wards using server-side CQL filtering`);
      // Cache the result
      boundaryCache.set(cacheKey, {
        data: wards,
        timestamp: Date.now()
      });
      return wards;
    } else {
      throw new Error('GeoNode WFS did not return a valid FeatureCollection');
    }
  } catch (error) {
    console.error('Failed to load wards from GeoNode WFS:', error);
    console.error('Error details:', { 
      message: error instanceof Error ? error.message : 'Unknown error', 
      municipality: municipalityName || 'none',
      municipalityCode: municipalityCode || 'none',
      district: districtCode || 'none',
      province: province || 'none',
      filterCount: (municipalityCode ? 1 : 0) + (districtCode ? 1 : 0) + (province ? 1 : 0),
      urlPrefix: 'https://*************/geoserver/geonode/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=geonode%3Asa_wards2020'
    });
    
    // Return hard-coded fallback wards for the selected municipality/district
    console.warn('Using fallback wards data due to API error');
    let fallbackWards: WardInfo[] = [];
    
    // Generate some fallback data (simplified)
    if (municipalityCode) {
      // Create 5 example wards for the selected municipality
      for (let i = 1; i <= 5; i++) {
        fallbackWards.push({
          id: `${municipalityCode}${i.toString().padStart(3, '0')}`,
          name: `Ward ${i} (${municipalityCode}_${i})`,
          tlc: municipalityCode,
          coordinates: [0, 0],
          properties: {
            wardno: i,
            wardlabel: `${municipalityCode}_${i}`,
            cat_b: municipalityCode
          }
        });
      }
    } else if (districtCode) {
      // Create 3 example wards for the district
      for (let i = 1; i <= 3; i++) {
        fallbackWards.push({
          id: `${districtCode}${i.toString().padStart(3, '0')}`,
          name: `Ward ${i} (${districtCode}_${i})`,
          tlc: districtCode,
          coordinates: [0, 0],
          properties: {
            wardno: i,
            wardlabel: `${districtCode}_${i}`,
            districtco: districtCode
          }
        });
      }
    } else if (province) {
      // Create example wards for a province
      for (let i = 1; i <= 5; i++) {
        const wardId = `${province}W${i.toString().padStart(3, '0')}`;
        fallbackWards.push({
          id: wardId,
          name: `Ward ${i} (${province} Example)`,
          tlc: province,
          coordinates: [0, 0],
          properties: {
            wardno: i,
            wardlabel: `${province}_${i}`,
            province: province
          }
        });
      }
    } else {
      // Generic fallback wards
      fallbackWards = [
        { id: 'WARD001', name: 'Ward 1 (Example)', tlc: 'MUNI', coordinates: [0, 0], properties: { wardno: 1 } },
        { id: 'WARD002', name: 'Ward 2 (Example)', tlc: 'MUNI', coordinates: [0, 0], properties: { wardno: 2 } },
        { id: 'WARD003', name: 'Ward 3 (Example)', tlc: 'MUNI', coordinates: [0, 0], properties: { wardno: 3 } },
        { id: 'WARD004', name: 'Ward 4 (Example)', tlc: 'MUNI', coordinates: [0, 0], properties: { wardno: 4 } },
        { id: 'WARD005', name: 'Ward 5 (Example)', tlc: 'MUNI', coordinates: [0, 0], properties: { wardno: 5 } }
      ];
    }
    
    // Cache the fallback result
    boundaryCache.set(cacheKey, {
      data: fallbackWards,
      timestamp: Date.now()
    });
    
    return fallbackWards;
  }
};



/**
 * Clear the boundary cache - either completely or for a specific key pattern
 */
export const clearBoundaryCache = (keyPattern?: string): void => {
  if (keyPattern) {
    // Clear only cache entries that match the pattern
    const keysToDelete: string[] = [];
    boundaryCache.forEach((_, key) => {
      if (key.includes(keyPattern)) {
        keysToDelete.push(key);
      }
    });
    
    keysToDelete.forEach(key => boundaryCache.delete(key));
    console.log(`Administrative boundary cache entries matching '${keyPattern}' cleared: ${keysToDelete.length} entries`);
  } else {
    // Clear the entire cache
    boundaryCache.clear();
    console.log('Administrative boundary cache completely cleared');
  }
};

/**
 * Get all administrative boundary data
 */
export const loadAllAdministrativeBoundaries = async (): Promise<AdministrativeBoundaryData> => {
  try {
    const [provinces, municipalities, districts, wards] = await Promise.all([
      loadProvinces(),
      loadMunicipalities(),
      loadDistricts(),
      loadWards()
    ]);

    return {
      provinces,
      municipalities,
      districts,
      wards
    };
  } catch (error) {
    console.error('Failed to load administrative boundaries:', error);
    throw error;
  }
};
