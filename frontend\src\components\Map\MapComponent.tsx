// Map component for SANSA Flood Monitoring
import React, { useEffect, useState, useCallback } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>, TileLayer, WMSTileLayer,
  useMap, Popup, Marker, Polygon, LayerGroup, useMapEvents
} from 'react-leaflet';
import L from 'leaflet';
import 'leaflet-draw'; // Import leaflet-draw JavaScript
import { fetchAvailableWMSLayers, WMSLayer, fetchFeatureInfo, GetFeatureInfoParams } from '../../services/geoserverService';
import { discoverLayers } from '../../services/discoveryService';
import { fetchFloodRiskData, fetchLayerData } from '../../services/mapService';
import { API_CONFIG } from '../../config';
import MapLegendPanel from './MapLegend';
import LoadingOverlay from './LoadingOverlay';
import FeatureInfoPopup, { FeatureInfo } from './FeatureInfoPopup';
import CustomFloodRiskLegend from './CustomFloodRiskLegend';
import TemporalSelectionModal from '../AOI/TemporalSelectionModal';
import AOIPreviewModal from '../AOI/AOIPreviewModal';
import { X, Layers } from 'lucide-react';
import { Badge } from 'react-bootstrap';
import './MapComponent.css';

interface MapComponentProps {
  selectedLayerNames: string[];
  dateRange: {
    startDate: string;
    endDate: string;
  };
  selectedTime?: string; // Add selectedTime prop for temporal layers
  onDrawComplete: (layers: any) => void;
  // AOI functionality
  isDrawingMode: boolean;
  onDrawModeChange: (isDrawing: boolean) => void;
  onAOIComplete: (aoiData: any, dateRange: any) => void;
  sidebarCollapsed?: boolean; // Add sidebarCollapsed prop for map resize
  // Basemap functionality
  selectedBasemap?: string;
  onBasemapChange?: (basemapName: string) => void;
  // Legend user mode
  legendUserMode?: 'simple' | 'advanced';
  onLegendUserModeChange?: (mode: 'simple' | 'advanced') => void;
  // Legend panel visibility
  showLegendPanel?: boolean;
  onCloseLegendPanel?: () => void;
  // Coordinate functionality
  isCoordinatePinMode?: boolean;
  onCoordinateSelected?: (latlng: {lat: number, lng: number}) => void;
}

const BoundsCapture = ({ onBoundsChange }: { onBoundsChange: (bounds: any) => void }) => {
  const map = useMap();

  useEffect(() => {
    if (!map) return;
    const updateBounds = () => onBoundsChange(map.getBounds());
    map.on('moveend', updateBounds);
    updateBounds();
    return () => {
      map.off('moveend', updateBounds);
    };
  }, [map, onBoundsChange]);

  return null;
};

const MapRecentre = ({ trigger, center, zoom }: { trigger: boolean, center: [number, number], zoom: number }) => {
  const map = useMap();
  useEffect(() => {
    if (trigger) map.setView(center, zoom);
  }, [trigger, center, zoom, map]);
  return null;
};

// Map click handler for feature info and coordinate pin
const MapClickHandler = ({
  wmsLayers,
  onFeatureInfoClick,
  isDrawingMode,
  isCoordinatePinMode,
  onCoordinateSelected,
  setPinCoordinates
}: {
  wmsLayers: WMSLayer[];
  onFeatureInfoClick: (event: any, queryableLayers: WMSLayer[]) => void;
  isDrawingMode: boolean;
  isCoordinatePinMode?: boolean;
  onCoordinateSelected?: (latlng: {lat: number, lng: number}) => void;
  setPinCoordinates?: (latlng: {lat: number, lng: number} | null) => void;
}) => {
  useMapEvents({
    click: (event) => {
      // Handle coordinate pin mode with highest priority
      if (isCoordinatePinMode) {
        // Update local pin coordinates for marker display
        if (setPinCoordinates) {
          setPinCoordinates(event.latlng);
        }
        
        // Call the handler passed from the parent component
        if (onCoordinateSelected) {
          onCoordinateSelected(event.latlng);
        }
        return;
      }
      
      // Don't handle feature info clicks when in drawing mode
      if (isDrawingMode) {
        return;
      }

      // Filter for queryable layers that are currently visible
      const queryableLayers = wmsLayers.filter(layer => layer.queryable === true);

      if (queryableLayers.length > 0) {
        onFeatureInfoClick(event, queryableLayers);
      }
    }
  });

  return null;
};

// Component to handle map resize when sidebar state changes
const MapResizeHandler = ({ sidebarCollapsed }: { sidebarCollapsed?: boolean }) => {
  const map = useMap();

  useEffect(() => {
    if (map) {
      // Delay the resize to allow CSS transition to complete
      const timer = setTimeout(() => {
        map.invalidateSize();
      }, 350); // Slightly longer than CSS transition (300ms)

      return () => clearTimeout(timer);
    }
  }, [sidebarCollapsed, map]);

  return null;
};

// Drawing controller to programmatically start drawing
const DrawingController = ({
  isDrawingMode,
  onCreated
}: {
  isDrawingMode: boolean;
  onCreated: (e: any) => void;
}) => {
  const map = useMap();
  const [drawControl, setDrawControl] = useState<any>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentDrawer, setCurrentDrawer] = useState<any>(null);

  useEffect(() => {
    if (!map) return;

    // Create draw control
    const drawnItems = new L.FeatureGroup();
    map.addLayer(drawnItems);

    const drawControlInstance = new L.Control.Draw({
      position: 'topright',
      draw: {
        polygon: {
          allowIntersection: false,
          drawError: {
            color: '#e1e100',
            message: '<strong>Error:</strong> Shape edges cannot cross!'
          },
          shapeOptions: {
            color: '#0a4273',
            weight: 2,
            fillOpacity: 0.2
          }
        },
        rectangle: false,
        circle: false,
        circlemarker: false,
        marker: false,
        polyline: false
      },
      edit: {
        featureGroup: drawnItems
      }
    });

    map.addControl(drawControlInstance);
    setDrawControl(drawControlInstance);

    // Listen for draw events
    map.on(L.Draw.Event.CREATED, (e: any) => {
      drawnItems.addLayer(e.layer);
      onCreated(e);
      setIsDrawing(false);
    });

    return () => {
      map.removeControl(drawControlInstance);
      map.removeLayer(drawnItems);
      map.off(L.Draw.Event.CREATED);
    };
  }, [map, onCreated]);

  useEffect(() => {
    if (!drawControl || !map) return;

    if (isDrawingMode && !isDrawing) {
      // Programmatically start polygon drawing
      const polygonDrawer = new L.Draw.Polygon(map as any, drawControl.options.draw.polygon);
      polygonDrawer.enable();
      setCurrentDrawer(polygonDrawer);
      setIsDrawing(true);
    } else if (!isDrawingMode && isDrawing && currentDrawer) {
      // Cancel current drawing
      currentDrawer.disable();
      setCurrentDrawer(null);
      setIsDrawing(false);
    }
  }, [isDrawingMode, drawControl, map, isDrawing, currentDrawer]);

  return null;
};

const MapComponent: React.FC<MapComponentProps> = ({
  selectedLayerNames,
  dateRange,
  selectedTime,
  onDrawComplete,
  isDrawingMode,
  onDrawModeChange,
  onAOIComplete,
  sidebarCollapsed,
  selectedBasemap = 'osm:osm',
  onBasemapChange,
  legendUserMode = 'simple',
  isCoordinatePinMode = false,
  onCoordinateSelected,
  onLegendUserModeChange,
  showLegendPanel = false,
  onCloseLegendPanel
}) => {
  const [center] = useState<[number, number]>([-29.0, 24.0]);
  const [zoom] = useState<number>(6);
  const [wmsLayers, setWmsLayers] = useState<WMSLayer[]>([]);
  const [mapBounds, setMapBounds] = useState<any>(null);
  const [loadingLayers, setLoadingLayers] = useState<{ [key: string]: boolean }>({});
  const [layerProgress, setLayerProgress] = useState<{ [key: string]: number }>({});
  const [floodRiskAreas, setFloodRiskAreas] = useState<any[]>([]);
  const [dwsVillages, setDwsVillages] = useState<any[]>([]);
  const [activePopup, setActivePopup] = useState<string | null>(null);
  const [errorLayers, setErrorLayers] = useState<{ [key: string]: string }>({});
  const [pinCoordinates, setPinCoordinates] = useState<{ lat: number, lng: number } | null>(null);
  
  // Reset pin coordinates when exiting coordinate pin mode
  useEffect(() => {
    if (!isCoordinatePinMode) {
      setPinCoordinates(null);
    }
  }, [isCoordinatePinMode]);

  const [layerOpacities, setLayerOpacities] = useState<{ [layerName: string]: number }>({});

  // Legend selection state
  const [selectedLegendLayer, setSelectedLegendLayer] = useState<string | undefined>();

  // Auto-select the last selected layer when layers change
  useEffect(() => {
    console.log('🔄 MapComponent: selectedLayerNames changed:', selectedLayerNames);
    console.log('🔄 MapComponent: Available WMS layers:', wmsLayers.map(l => l.name));

    // Check for exact matches
    const matchingLayers = wmsLayers.filter(wmsLayer =>
      selectedLayerNames.includes(wmsLayer.name)
    );
    console.log('🎯 MapComponent: Matching layers found:', matchingLayers.map(l => l.name));

    // Run comprehensive debug summary
    if (selectedLayerNames.length > 0 && wmsLayers.length > 0) {
      debugLayerMatching();
    }

    if (selectedLayerNames.length > 0 && !selectedLegendLayer) {
      // Select the last layer in the list (most recently added)
      setSelectedLegendLayer(selectedLayerNames[selectedLayerNames.length - 1]);
    } else if (selectedLayerNames.length === 0) {
      // Clear selection if no layers are visible
      setSelectedLegendLayer(undefined);
    } else if (selectedLegendLayer && !selectedLayerNames.includes(selectedLegendLayer)) {
      // If selected layer is no longer visible, select the last visible layer
      setSelectedLegendLayer(selectedLayerNames[selectedLayerNames.length - 1]);
    }
  }, [selectedLayerNames, selectedLegendLayer, wmsLayers]);

  // Handle manual legend layer selection
  const handleLegendLayerSelect = (layerName: string) => {
    setSelectedLegendLayer(layerName);
  };

  // Feature info popup state
  const [featureInfo, setFeatureInfo] = useState<FeatureInfo | null>(null);

  // AOI state management
  const [drawnPolygon, setDrawnPolygon] = useState<any>(null);
  const [showTemporalModal, setShowTemporalModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [aoiDateRange, setAoiDateRange] = useState<{ startDate: string; endDate: string } | null>(null);

  // Track last toggled layer for expanded legend (now using selectedLegendLayer)
  const [lastToggledLayer, setLastToggledLayer] = useState<WMSLayer | null>(null);

  // Update lastToggledLayer when selectedLegendLayer changes
  useEffect(() => {
    if (selectedLegendLayer) {
      const selectedLayer = wmsLayers.find(layer => layer.name === selectedLegendLayer);
      if (selectedLayer) {
        setLastToggledLayer(selectedLayer);
      }
    }
  }, [selectedLegendLayer, wmsLayers]);
  const [popupPosition, setPopupPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });

  // Removed isSentinelLayer - using dynamic layer properties instead

  const getLayerBounds = useCallback((bbox: any) => {
    if (!bbox) return undefined;

    const { miny, minx, maxy, maxx } = bbox;

    // Convert to numbers and validate
    const minY = Number(miny);
    const minX = Number(minx);
    const maxY = Number(maxy);
    const maxX = Number(maxx);

    // Check for invalid coordinates
    if (isNaN(minY) || isNaN(minX) || isNaN(maxY) || isNaN(maxX)) {
      console.warn('Invalid bbox coordinates detected:', { miny, minx, maxy, maxx });
      return undefined; // Let Leaflet use default bounds
    }

    // Validate coordinate ranges
    if (minY < -90 || minY > 90 || maxY < -90 || maxY > 90 ||
        minX < -180 || minX > 180 || maxX < -180 || maxX > 180) {
      console.warn('Bbox coordinates out of valid range:', { minY, minX, maxY, maxX });
      return undefined; // Let Leaflet use default bounds
    }

    // Ensure min < max
    if (minY >= maxY || minX >= maxX) {
      console.warn('Invalid bbox: min >= max:', { minY, minX, maxY, maxX });
      return undefined; // Let Leaflet use default bounds
    }

    return [[minY, minX], [maxY, maxX]] as [[number, number], [number, number]];
  }, []);

  const isLayerVisible = useCallback((layer: WMSLayer) => {
    if (!layer || !layer.name) {
      console.log('🚨 Layer visibility check: Invalid layer', layer);
      return false;
    }

    // Simple check: layer is visible if it's in the selected layers array
    // Some layer names might include namespace prefixes that need to be stripped
    const layerBaseName = layer.name.includes(':') ? layer.name.split(':')[1] : layer.name;
    const isVisible = selectedLayerNames.includes(layer.name) || selectedLayerNames.includes(layerBaseName);

    // Enhanced debug logging
    if (selectedLayerNames.length > 0) {
      console.log(`🔍 Layer visibility check:`, {
        layerName: layer.name,
        layerBaseName,
        layerTitle: layer.title,
        selectedLayers: selectedLayerNames,
        isVisible,
        exactMatch: selectedLayerNames.find(selected => selected === layer.name || selected === layerBaseName)
      });
    }

    return isVisible;
  }, [selectedLayerNames]);



  const handleLayerOpacityChange = useCallback((layerName: string, newOpacity: number) => {
    setLayerOpacities(prev => ({
      ...prev,
      [layerName]: newOpacity,
    }));
  }, []);

  // Cleanup opacity state when layers are removed
  useEffect(() => {
    const visibleLayerNames = wmsLayers.filter(isLayerVisible).map(layer => layer.name);
    setLayerOpacities(prev => {
      const cleaned = { ...prev };
      Object.keys(cleaned).forEach(layerName => {
        if (!visibleLayerNames.includes(layerName)) {
          delete cleaned[layerName];
        }
      });
      return cleaned;
    });
  }, [wmsLayers]); // Removed isLayerVisible from dependencies to prevent re-renders

  useEffect(() => {
    const loadAllLayers = async () => {
      try {
        console.log('🗺️ MapComponent: Loading all layers (local + remote)...');
        console.log('🌐 WMS Capabilities URL:', `${API_CONFIG.BASE_URL}/ows/capabilities`);
        console.log('🌐 WMS Proxy URL:', `${API_CONFIG.BASE_URL}/ows/wms-proxy`);
        console.log('🌐 Discovery URL:', `${API_CONFIG.BASE_URL}/datasets/geonode/discovery`);

        // Load layers from discovery service (includes both local and remote)
        const discoveryResult = await discoverLayers();
        const allLayers = discoveryResult.layers || [];

        console.log('🗺️ MapComponent: Loaded layers from discovery:', allLayers.length);
        console.log('🗺️ MapComponent: Layer breakdown:', {
          total: allLayers.length,
          local: allLayers.filter(l => !(l as any).isRemote).length,
          remote: allLayers.filter(l => (l as any).isRemote).length
        });

        // Convert discovery layers to WMSLayer format for compatibility
        const wmsCompatibleLayers = allLayers.map(layer => {
          // Handle different bbox formats
          let bbox;
          const layerBbox = (layer as any).bbox;

          if (layerBbox && Array.isArray(layerBbox) && layerBbox.length === 4) {
            // Array format: [minx, miny, maxx, maxy]
            bbox = {
              minx: layerBbox[0],
              miny: layerBbox[1],
              maxx: layerBbox[2],
              maxy: layerBbox[3]
            };
          } else if (layerBbox && typeof layerBbox === 'object') {
            // Object format: {minx, miny, maxx, maxy}
            bbox = {
              minx: layerBbox.minx || layerBbox[0] || -180,
              miny: layerBbox.miny || layerBbox[1] || -90,
              maxx: layerBbox.maxx || layerBbox[2] || 180,
              maxy: layerBbox.maxy || layerBbox[3] || 90
            };
          } else {
            // Default global bounds
            bbox = {
              minx: -180,
              miny: -90,
              maxx: 180,
              maxy: 90
            };
          }

          // Validate bbox values
          if (isNaN(bbox.minx) || isNaN(bbox.miny) || isNaN(bbox.maxx) || isNaN(bbox.maxy)) {
            console.warn(`Invalid bbox for layer ${layer.name}, using global bounds`);
            bbox = { minx: -180, miny: -90, maxx: 180, maxy: 90 };
          }

          return {
            name: layer.name,
            title: layer.title || layer.name,
            type: 'raster',
            queryable: (layer as any).queryable || false,
            bbox: bbox,
            // Add remote layer properties
            isRemote: (layer as any).isRemote || false,
            serviceType: (layer as any).serviceType,
            remoteUrl: (layer as any).remoteUrl,
            url: (layer as any).url
          };
        });

        console.log('🗺️ MapComponent: Converted layers:', wmsCompatibleLayers.map(l => ({
          name: l.name,
          title: l.title,
          isRemote: (l as any).isRemote,
          serviceType: (l as any).serviceType
        })));

        setWmsLayers(wmsCompatibleLayers as WMSLayer[]);
      } catch (error) {
        console.error('🚨 MapComponent: Failed to load layers from discovery:', error);

        // Fallback to local WMS layers only
        try {
          console.log('🔄 MapComponent: Falling back to local WMS layers...');
          const localLayers = await fetchAvailableWMSLayers();
          setWmsLayers(localLayers);
        } catch (fallbackError) {
          console.error('🚨 MapComponent: Fallback also failed:', fallbackError);
          setWmsLayers([]);
        }
      }
    };
    loadAllLayers();
  }, []);

  useEffect(() => {
    if (selectedLayerNames.includes('floodRisk') && mapBounds) {
      fetchFloodRiskData(mapBounds, dateRange)
        .then(setFloodRiskAreas)
        .catch(console.error);
    } else {
      setFloodRiskAreas([]);
    }
  }, [selectedLayerNames, dateRange, mapBounds]);

  useEffect(() => {
    if (selectedLayerNames.includes('dwsVillage') && mapBounds) {
      fetchLayerData('dwsVillage', mapBounds)
        .then(setDwsVillages)
        .catch(console.error);
    } else {
      setDwsVillages([]);
    }
  }, [selectedLayerNames, mapBounds]);

  // Track the last toggled layer for expanded legend
  useEffect(() => {
    // Enhanced debug logging to trace layer visibility issues
    console.log('🔎 DEBUG - Layer Visibility Status:', {
      selectedLayerNames,
      availableWmsLayers: wmsLayers.map(l => l.name),
      matchingLayers: wmsLayers.filter(layer => selectedLayerNames.includes(layer.name)).map(l => l.name),
      wmsConfig: `${API_CONFIG.BASE_URL}/ows/wms-proxy`
    });
    
    const visibleLayers = wmsLayers.filter(layer => selectedLayerNames.includes(layer.name));
    if (visibleLayers.length > 0) {
      // Set the last layer in the visible layers array as the last toggled
      setLastToggledLayer(visibleLayers[visibleLayers.length - 1]);
    } else {
      setLastToggledLayer(null);
    }
  }, [selectedLayerNames, wmsLayers]);

  const handleCreated = useCallback((e: any) => {
    if (e.layerType === 'polygon') {
      const geoJSON = e.layer.toGeoJSON();

      // If in AOI drawing mode, start the AOI workflow
      if (isDrawingMode) {
        setDrawnPolygon(geoJSON);
        onDrawModeChange(false); // Exit drawing mode
        setShowTemporalModal(true); // Show temporal selection modal
      } else {
        // Regular drawing functionality
        onDrawComplete(geoJSON);
      }
    }
  }, [onDrawComplete, isDrawingMode, onDrawModeChange]);

  // Handle feature info click
  const handleFeatureInfoClick = useCallback(async (event: any, queryableLayers: WMSLayer[]) => {
    const { latlng, containerPoint } = event;

    // Set popup position based on click coordinates
    setPopupPosition({ x: containerPoint.x + 10, y: containerPoint.y - 10 });

    try {
      // For now, query the first queryable layer
      // TODO: In the future, we could query multiple layers and combine results
      const layer = queryableLayers[0];

      // Get map container to calculate dimensions
      const mapContainer = event.target.getContainer();
      const mapSize = event.target.getSize();
      const bounds = event.target.getBounds();

      const params: GetFeatureInfoParams = {
        layers: layer.name,
        queryLayers: layer.name,
        x: Math.round(containerPoint.x),
        y: Math.round(containerPoint.y),
        width: mapSize.x,
        height: mapSize.y,
        bbox: `${bounds.getWest()},${bounds.getSouth()},${bounds.getEast()},${bounds.getNorth()}`,
        srs: 'EPSG:4326',
        infoFormat: 'application/json',
        featureCount: 10
      };

      const response = await fetchFeatureInfo(params);

      // Parse GeoServer response (format may vary)
      let features = [];
      if (response && response.features) {
        features = response.features;
      } else if (response && Array.isArray(response)) {
        features = response;
      } else if (response && typeof response === 'object') {
        // Handle other response formats
        features = [{ properties: response }];
      }
      
      if (features.length > 0) {
        setFeatureInfo({
          layerName: layer.title || layer.name,
          features: features,
          coordinates: {
            lat: latlng.lat,
            lng: latlng.lng
          }
        });
      }
    } catch (error) {
      console.error('Error fetching feature info:', error);
      // Could show an error message to user here
    }
  }, []);

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high': return 'red';
      case 'moderate': return 'yellow';
      case 'low': return 'green';
      default: return 'blue';
    }
  };

  const handleMarkerClick = useCallback((layerName: string) => {
    setActivePopup(prev => prev === layerName ? null : layerName);
  }, []);

  const handleLayerLoading = useCallback((layerName: string) => {
    setLoadingLayers(prev => ({ ...prev, [layerName]: true }));
    setLayerProgress(prev => ({ ...prev, [layerName]: 0 }));
  }, []);

  const handleLayerLoad = useCallback((layerName: string) => {
    setLoadingLayers(prev => ({ ...prev, [layerName]: false }));
    setLayerProgress(prev => ({ ...prev, [layerName]: 100 }));
    setErrorLayers(prev => {
      const updated = { ...prev };
      delete updated[layerName];
      return updated;
    });

    // Clear the completed loading state after a short delay to prevent lingering
    setTimeout(() => {
      setLayerProgress(prev => {
        const updated = { ...prev };
        delete updated[layerName];
        return updated;
      });
    }, 2000);
  }, []);

  const handleLayerError = useCallback((layerName: string, error: any) => {
    console.error(`🚨 Layer error for "${layerName}":`, error);
    setLoadingLayers(prev => ({ ...prev, [layerName]: false }));
    setErrorLayers(prev => ({ ...prev, [layerName]: error.message || 'Failed to load layer' }));
  }, []);

  // Debug summary function
  const debugLayerMatching = useCallback(() => {
    const baseWmsUrl = `${API_CONFIG.BASE_URL}/ows/wms-proxy`;

    console.log('\n🔍 === LAYER MATCHING DEBUG SUMMARY ===');
    console.log('🌐 Base WMS URL:', baseWmsUrl);
    console.log('📋 Selected Layer Names:', selectedLayerNames);
    console.log('🗺️ Available WMS Layers:', wmsLayers.map(l => l.name));
    console.log('🎯 Exact Matches:', wmsLayers.filter(wms => selectedLayerNames.includes(wms.name)).map(l => l.name));
    console.log('❌ Missing in WMS:', selectedLayerNames.filter(selected => !wmsLayers.find(wms => wms.name === selected)));
    console.log('🔄 Visible Layers:', wmsLayers.filter(layer => isLayerVisible(layer)).map(l => l.name));
    console.log('🚨 Error Layers:', Object.keys(errorLayers));
    console.log('⏳ Loading Layers:', Object.keys(loadingLayers).filter(name => loadingLayers[name]));

    // Show example URLs for selected layers
    const visibleLayers = wmsLayers.filter(layer => isLayerVisible(layer));
    if (visibleLayers.length > 0) {
      console.log('🔗 Example WMS URLs for visible layers:');
      visibleLayers.forEach(layer => {
        const exampleParams = new URLSearchParams({
          SERVICE: 'WMS',
          VERSION: '1.1.1',
          REQUEST: 'GetMap',
          LAYERS: layer.name,
          STYLES: '',
          FORMAT: 'image/png',
          TRANSPARENT: 'true',
          SRS: 'EPSG:4326',
          BBOX: '-180,-90,180,90',
          WIDTH: '256',
          HEIGHT: '256'
        });
        const fullUrl = `${baseWmsUrl}?${exampleParams.toString()}`;
        console.log(`  📍 ${layer.name}: ${fullUrl}`);
      });
    }

    console.log('=== END DEBUG SUMMARY ===\n');
  }, [selectedLayerNames, wmsLayers, isLayerVisible, errorLayers, loadingLayers]);

  // Function to test WMS URL accessibility (call from console)
  const testWmsUrl = useCallback(async (layerName: string) => {
    const baseUrl = `${API_CONFIG.BASE_URL}/ows/wms-proxy`;
    const testParams = new URLSearchParams({
      SERVICE: 'WMS',
      VERSION: '1.1.1',
      REQUEST: 'GetMap',
      LAYERS: layerName,
      STYLES: '',
      FORMAT: 'image/png',
      TRANSPARENT: 'true',
      SRS: 'EPSG:4326',
      BBOX: '-180,-90,180,90',
      WIDTH: '256',
      HEIGHT: '256'
    });
    const testUrl = `${baseUrl}?${testParams.toString()}`;

    console.log(`🧪 Testing WMS URL for layer "${layerName}":`, testUrl);

    try {
      const response = await fetch(testUrl);
      console.log(`✅ WMS URL test result:`, {
        status: response.status,
        statusText: response.statusText,
        contentType: response.headers.get('content-type'),
        url: testUrl
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`🚨 WMS Error Response:`, errorText);
      }
    } catch (error) {
      console.error(`🚨 WMS URL test failed:`, error);
    }
  }, []);

  // Make testWmsUrl available globally for console debugging
  useEffect(() => {
    (window as any).testWmsUrl = testWmsUrl;
    (window as any).debugLayerMatching = debugLayerMatching;

    return () => {
      delete (window as any).testWmsUrl;
      delete (window as any).debugLayerMatching;
    };
  }, [testWmsUrl, debugLayerMatching]);

  const handleCloseLoader = useCallback(() => {
    setLoadingLayers({});
    setErrorLayers({});
    setLayerProgress({});
  }, []);

  // AOI workflow handlers
  const handleTemporalConfirm = useCallback((dateRange: { startDate: string; endDate: string }) => {
    setAoiDateRange(dateRange);
    setShowTemporalModal(false);
    setShowPreviewModal(true);
  }, []);

  const handleAOIDownload = useCallback((selectedLayers: string[], dateRange: { startDate: string; endDate: string }) => {
    if (drawnPolygon) {
      // Calculate AOI bounds and area
      const coordinates = drawnPolygon.geometry.coordinates[0];
      const lats = coordinates.map((coord: number[]) => coord[1]);
      const lngs = coordinates.map((coord: number[]) => coord[0]);

      const bounds = {
        north: Math.max(...lats),
        south: Math.min(...lats),
        east: Math.max(...lngs),
        west: Math.min(...lngs)
      };

      // Simple area calculation (approximate)
      const area = Math.abs((bounds.east - bounds.west) * (bounds.north - bounds.south)) * 111 * 111; // rough km²

      const aoiData = {
        coordinates: drawnPolygon,
        bounds,
        area,
        selectedLayers,
        dateRange
      };

      onAOIComplete(aoiData, dateRange);
      setShowPreviewModal(false);
      setDrawnPolygon(null);
      setAoiDateRange(null);
    }
  }, [drawnPolygon, onAOIComplete]);

  return (
    <div className="map-wrapper">
      <MapContainer
        center={center}
        zoom={zoom}
        scrollWheelZoom
        className={`map ${isDrawingMode ? 'drawing-mode' : ''}`}
        worldCopyJump
        maxBoundsViscosity={1.0}
      >
        <MapRecentre trigger={selectedLayerNames.length === 0} center={center} zoom={zoom} />
        {/* Dynamic Basemap Layer */}
        {selectedBasemap === 'osm:osm' ? (
          <TileLayer
            attribution='&copy; OpenStreetMap contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />
        ) : (
          <WMSTileLayer
            key={`basemap-${selectedBasemap}`}
            url={`${API_CONFIG.BASE_URL}/ows/wms-proxy`}
            layers={selectedBasemap}
            format="image/jpeg"
            transparent={false}
            version="1.1.1"
            attribution="GeoServer Basemap"
            opacity={1}
            maxZoom={19}
            minZoom={2}
            tileSize={256}
          />
        )}
        <LayerGroup>
          {/* Show warnings for selected layers that can't be found */}
          {selectedLayerNames.length > 0 && (
            <div className="layer-warnings" style={{ 
              position: 'absolute', 
              zIndex: 1000, 
              top: 10, 
              right: 10, 
              maxWidth: '300px', 
              pointerEvents: 'none' 
            }}>
              {selectedLayerNames.filter(name => !wmsLayers.some(layer => 
                layer.name === name || (layer.name.includes(':') && layer.name.split(':')[1] === name)
              )).map(name => (
                <div key={`warning-${name}`} style={{ 
                  backgroundColor: 'rgba(255, 193, 7, 0.8)', 
                  color: '#212529', 
                  padding: '5px 10px',
                  borderRadius: '4px', 
                  marginBottom: '5px',
                  fontSize: '12px',
                  fontWeight: 'bold'
                }}>
                  Layer not found: {name}
                </div>
              ))}
            </div>
          )}
          {wmsLayers.map(layer => {
            const isVisible = isLayerVisible(layer);
            console.log(`🗺️ Rendering layer "${layer.name}": visible=${isVisible}, isRemote=${(layer as any).isRemote}`);

            if (!isVisible) return null;

            const bounds = getLayerBounds(layer.bbox);
            console.log(`🗺️ Layer "${layer.name}" bounds:`, bounds);

            // Skip layer if bounds are invalid
            if (bounds && bounds.some(coord => coord.some(val => isNaN(val)))) {
              console.error(`🚨 Skipping layer "${layer.name}" due to invalid bounds:`, bounds);
              return null;
            }

            // Check if this is a remote layer
            const isRemoteLayer = (layer as any).isRemote;
            const serviceType = (layer as any).serviceType;
            const remoteUrl = (layer as any).remoteUrl;

            // Dynamic format detection based on layer properties
            const format = (layer as any).formats?.includes('image/png') ? 'image/jpeg' : 'image/png';
            const transparent = format === 'image/png';

            // Determine the appropriate URL based on layer type
            let layerUrl: string;
            let layerName: string;

            // Always use the WMS proxy for both local and remote layers
            // The proxy will handle the conversion for remote layers
            layerUrl = `${API_CONFIG.BASE_URL}/ows/wms-proxy`;
            layerName = layer.name;

            if (isRemoteLayer) {
              console.log(`🌐 Remote layer "${layer.name}" - Service: ${serviceType}, Remote URL: ${remoteUrl}, Proxy URL: ${layerUrl}`);
            } else {
              console.log(`🏠 Local layer "${layer.name}" - URL: ${layerUrl}`);
            }

            // Construct example WMS GetMap URL for debugging
            const exampleWmsParams = new URLSearchParams({
              SERVICE: 'WMS',
              VERSION: '1.1.1',
              REQUEST: 'GetMap',
              LAYERS: layerName,
              STYLES: '',
              FORMAT: format,
              TRANSPARENT: transparent.toString(),
              SRS: 'EPSG:4326',
              BBOX: '-180,-90,180,90', // Example bbox
              WIDTH: '256',
              HEIGHT: '256'
            });
            const fullExampleUrl = `${layerUrl}?${exampleWmsParams.toString()}`;

            console.log(`🗺️ WMS request for "${layer.name}":`, {
              baseUrl: layerUrl,
              layerName: layerName,
              format,
              transparent,
              bounds,
              isRemote: isRemoteLayer,
              serviceType,
              exampleGetMapUrl: fullExampleUrl
            });

            return (
              <WMSTileLayer
                key={layer.name}
                url={layerUrl}
                layers={layerName}
                format={format}
                transparent={transparent}
                version="1.1.1"
                bounds={bounds}
                attribution={`${layer.title}${isRemoteLayer ? ' (Remote)' : ''}`}
                opacity={layerOpacities[layer.name] ?? 1.0}
                maxZoom={19}
                minZoom={2}
                tileSize={256}
                // @ts-ignore - Adding TIME parameter for temporal layers
                params={selectedTime ? { time: selectedTime } : undefined}
                eventHandlers={{
                  loading: () => {
                    console.log(`🔄 Layer "${layer.name}" loading... (${isRemoteLayer ? 'Remote' : 'Local'})`);
                    handleLayerLoading(layer.name);
                  },
                  load: () => {
                    console.log(`✅ Layer "${layer.name}" loaded successfully (${isRemoteLayer ? 'Remote' : 'Local'})`);
                    handleLayerLoad(layer.name);
                  },
                  error: (e) => {
                    console.error(`🚨 Layer "${layer.name}" error (${isRemoteLayer ? 'Remote' : 'Local'}):`, e);
                    handleLayerError(layer.name, e);
                  },
                  tileerror: (e) => {
                    console.error(`🚨 Layer "${layer.name}" tile error (${isRemoteLayer ? 'Remote' : 'Local'}):`, e);
                    handleLayerError(layer.name, { message: 'Tile load error', details: e });
                  }
                }}
              />
            );
          })}
        </LayerGroup>
        <BoundsCapture onBoundsChange={setMapBounds} />
        <MapResizeHandler sidebarCollapsed={sidebarCollapsed} />
        <MapClickHandler
          wmsLayers={wmsLayers.filter(isLayerVisible)}
          onFeatureInfoClick={handleFeatureInfoClick}
          isDrawingMode={isDrawingMode}
          isCoordinatePinMode={isCoordinatePinMode}
          onCoordinateSelected={onCoordinateSelected}
          setPinCoordinates={setPinCoordinates}
        />
        <DrawingController
          isDrawingMode={isDrawingMode}
          onCreated={handleCreated}
        />
        {floodRiskAreas.map((area, i) => (
          <Polygon key={`flood-${i}`} positions={area.coordinates} pathOptions={{ color: getRiskColor(area.risk), fillOpacity: 0.4 }} />
        ))}
        {dwsVillages.map((village, i) => (
          <Marker
            key={`village-${i}`}
            position={[village.lat, village.lng]}
            eventHandlers={{ click: () => handleMarkerClick(`village-${i}`) }}
          >
            {activePopup === `village-${i}` && (
              <Popup>
                <h3>{village.name}</h3>
                <p>Population: {village.population}</p>
              </Popup>
            )}
          </Marker>
        ))}
        
        {/* Coordinate Pin Marker */}
        {isCoordinatePinMode && pinCoordinates && (
          <Marker
            key="coordinate-pin"
            position={[pinCoordinates.lat, pinCoordinates.lng]}
            icon={L.divIcon({
              className: 'coordinate-pin-marker',
              html: `<div style="background-color: #ff5722; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white;"></div>`,
              iconSize: [16, 16],
              iconAnchor: [8, 8]
            })}
          >
            <Popup>
              <div>
                <strong>Selected Coordinates</strong><br />
                Latitude: {pinCoordinates.lat.toFixed(6)}<br />
                Longitude: {pinCoordinates.lng.toFixed(6)}
              </div>
            </Popup>
          </Marker>
        )}
      </MapContainer>
      <LoadingOverlay
        loadingLayers={loadingLayers}
        wmsLayers={wmsLayers}
        errorLayers={errorLayers}
        layerProgress={layerProgress}
        onRetryLayer={() => { }} // retry implementation can be added as needed
        onCloseLoader={handleCloseLoader}
      />
      {showLegendPanel && (
        <MapLegendPanel
          visibleLayers={wmsLayers.filter(isLayerVisible)}
          mapHeight={800}
          layerOpacities={layerOpacities}
          onOpacityChange={handleLayerOpacityChange}
          userMode={legendUserMode}
          onUserModeChange={onLegendUserModeChange}
          selectedLegendLayer={selectedLegendLayer}
          onLegendLayerSelect={handleLegendLayerSelect}
          onClose={onCloseLegendPanel}
          autoShowInfo={true}
        />
      )}

      {/* Expanded Legend Panel - positioned on left, shows selected layer */}
      {lastToggledLayer && selectedLegendLayer && (
        <div className="expanded-legend-panel-container-left">
          <div className="expanded-legend-panel">
            <div className="expanded-legend-header">
              <h5 className="expanded-legend-title">
                <Layers size={18} className="me-2 header-icon" />
                Legend 
                {/* <Badge bg="light" text="dark" className="ms-2">
                  Selected
                </Badge> */}
              </h5>
            </div>
            <div className="expanded-legend-content">
              <div className="legend-content-layout">
                {lastToggledLayer.title || lastToggledLayer.name}
                <div className="original-legend-container">
                  <img
                    src={`${API_CONFIG.BASE_URL}/ows/wms-proxy?SERVICE=WMS&REQUEST=GetLegendGraphic&LAYER=${lastToggledLayer.name}&FORMAT=image/png&VERSION=1.1.1`}
                    alt={`Expanded legend for ${lastToggledLayer.name}`}
                    className="expanded-legend-image"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.onerror = null;
                      target.style.display = 'none';
                      const fallbackMsg = document.createElement('div');
                      fallbackMsg.innerText = 'Legend could not be loaded';
                      fallbackMsg.style.textAlign = 'center';
                      fallbackMsg.style.color = '#888';
                      fallbackMsg.style.padding = '20px';
                      target.parentElement?.appendChild(fallbackMsg);
                    }}
                  />
                </div>

                {/* Custom Flood Risk Legend - positioned to the right */}
                {/* <CustomFloodRiskLegend /> */}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Feature Info Popup */}
      {featureInfo && (
        <FeatureInfoPopup
          featureInfo={featureInfo}
          onClose={() => setFeatureInfo(null)}
          position={popupPosition}
        />
      )}

      {/* AOI Temporal Selection Modal */}
      <TemporalSelectionModal
        show={showTemporalModal}
        onHide={() => setShowTemporalModal(false)}
        onConfirm={handleTemporalConfirm}
        aoiCoordinates={drawnPolygon}
        preSelectedDateRange={dateRange}
      />

      {/* AOI Preview Modal */}
      {aoiDateRange && drawnPolygon && (
        <AOIPreviewModal
          show={showPreviewModal}
          onHide={() => setShowPreviewModal(false)}
          onDownload={handleAOIDownload}
          aoiData={{
            coordinates: drawnPolygon,
            bounds: {
              north: Math.max(...drawnPolygon.geometry.coordinates[0].map((coord: number[]) => coord[1])),
              south: Math.min(...drawnPolygon.geometry.coordinates[0].map((coord: number[]) => coord[1])),
              east: Math.max(...drawnPolygon.geometry.coordinates[0].map((coord: number[]) => coord[0])),
              west: Math.min(...drawnPolygon.geometry.coordinates[0].map((coord: number[]) => coord[0]))
            },
            area: Math.abs((Math.max(...drawnPolygon.geometry.coordinates[0].map((coord: number[]) => coord[0])) -
                          Math.min(...drawnPolygon.geometry.coordinates[0].map((coord: number[]) => coord[0]))) *
                         (Math.max(...drawnPolygon.geometry.coordinates[0].map((coord: number[]) => coord[1])) -
                          Math.min(...drawnPolygon.geometry.coordinates[0].map((coord: number[]) => coord[1])))) * 111 * 111
          }}
          dateRange={aoiDateRange}
          availableLayers={wmsLayers.map(layer => ({
            name: layer.name,
            title: layer.title,
            temporal: (layer as any).temporal?.hasTemporal || (layer as any).isTemporal,
            queryable: layer.queryable
          }))}
          selectedLayers={selectedLayerNames}
          selectedBasemap="osm:osm" // For now, use default - this could be passed as prop
        />
      )}
    </div>
  );
};

export default MapComponent;
