import { useState, useEffect } from 'react';
import { Container } from 'react-bootstrap';
import NavBar from './components/NavBar/NavBar';
import Sidebar from './components/Sidebar/Sidebar';
import MapComponent from './components/Map/MapComponent';
import AnalyticsDashboard from './components/Analytics/AnalyticsDashboard';
import ToolsPanel from './components/Tools/ToolsPanel';
import RegionalFilterModal from './components/RegionalFilter/RegionalFilterModal';
import DownloadModal from './components/Download/DownloadModal';
import { useDiscoveryLayers } from './hooks/useDiscoveryLayers';
import 'bootstrap/dist/css/bootstrap.min.css';
import './App.css';

function App() {
  const { layers, selectedLayers, handleLayerToggle, isLoading, error } = useDiscoveryLayers();

  const [currentView, setCurrentView] = useState<'map' | 'analytics'>('map');
  const [isToolsPanelOpen, setIsToolsPanelOpen] = useState(false);

  const [dateRange, setDateRange] = useState({
    startDate: '2025/05/20',
    endDate: '2025/05/20'
  });
  const [drawnItems, setDrawnItems] = useState<any>(null);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // AOI state management
  const [isDrawingMode, setIsDrawingMode] = useState(false);
  const [hasDrawnArea, setHasDrawnArea] = useState(false);

  // Regional AOI state management
  const [aoiMethod, setAOIMethod] = useState<'drawn' | 'regional'>('drawn');
  const [hasRegionalSelection, setHasRegionalSelection] = useState(false);
  const [showRegionalModal, setShowRegionalModal] = useState(false);
  const [regionalAOI, setRegionalAOI] = useState<any>(null);

  // Download modal state
  const [showDownloadModal, setShowDownloadModal] = useState(false);

  // Basemap state management
  const [selectedBasemap, setSelectedBasemap] = useState('osm:osm'); // Default to OpenStreetMap

  // Coordinate pin mode state
  const [coordinatePinMode, setCoordinatePinMode] = useState(false);
  const [currentCoordinates, setCurrentCoordinates] = useState('');

  // Legend user mode state
  const [legendUserMode, setLegendUserMode] = useState<'simple' | 'advanced'>('simple');

  // Legend panel visibility state
  const [showLegendPanel, setShowLegendPanel] = useState(false);
  const [selectedLegendLayerName, setSelectedLegendLayerName] = useState<string | null>(null);

  const handleDateChange = (type: 'startDate' | 'endDate', value: string) => {
    setDateRange(prev => ({ ...prev, [type]: value }));
  };

  const handleSearch = (query: string) => {
    console.log('Searching for:', query);
  };

  const handlePreviewData = () => {
    alert(`🔍 Data Preview Requested!\n\nLayers: ${selectedLayers.join(', ') || 'None'}\nDate Range: ${dateRange.startDate} to ${dateRange.endDate}\n\nCheck the console for details.`);
    console.log('Previewing data for layers:', selectedLayers);
    console.log('Date range:', dateRange);
    console.log('Region:', drawnItems);
  };

  const handleDownloadData = () => {
    console.log('Downloading data for layers:', selectedLayers);
    console.log('Date range:', dateRange);
    console.log('Region:', drawnItems);
    setShowDownloadModal(true);
  };

  const handleQueryTemporalData = () => {
    alert(`🔍 Temporal Query Executed!\n\nLayers: ${selectedLayers.join(', ') || 'None'}\nDate Range: ${dateRange.startDate} to ${dateRange.endDate}\n\nCheck the console for details.`);
    console.log('Temporal layers selected:', selectedLayers);
    console.log('Date range:', dateRange);
    console.log('Region:', drawnItems);
  };

  const handleToolsToggle = () => {
    setIsToolsPanelOpen(prev => !prev);
  };

  const handleToolsPanelClose = () => {
    setIsToolsPanelOpen(false);
  };

  // AOI handlers
  const handleDrawModeToggle = (isDrawing: boolean) => {
    setIsDrawingMode(isDrawing);
  };

  const handleClearDrawnArea = () => {
    setHasDrawnArea(false);
    setDrawnItems(null);
  };

  const handleAOIComplete = (aoiData: any, dateRange: any) => {
    console.log('AOI Complete:', aoiData, dateRange);
    setHasDrawnArea(true);
    // Here you would typically process the AOI data for download
  };

  const handleBasemapChange = (basemapName: string) => {
    setSelectedBasemap(basemapName);
    console.log('Basemap changed to:', basemapName);
  };

  // Handle coordinate pin mode toggle
  const handleCoordinatePinModeToggle = (enabled: boolean) => {
    setCoordinatePinMode(enabled);
    console.log('Coordinate pin mode:', enabled ? 'enabled' : 'disabled');
  };

  // Handle map click for coordinate pin
  const handleMapClick = (latlng: {lat: number, lng: number}) => {
    if (coordinatePinMode) {
      // Format coordinates to 6 decimal places
      const formattedCoordinates = `${latlng.lat.toFixed(6)}, ${latlng.lng.toFixed(6)}`;
      setCurrentCoordinates(formattedCoordinates);
      setCoordinatePinMode(false); // Turn off pin mode after placing
      console.log('Coordinates set to:', formattedCoordinates);
    }
  };

  // Handle layer info - show legend panel and trigger info modal
  const handleShowLayerInfo = (layerName: string) => {
    setSelectedLegendLayerName(layerName);
    setShowLegendPanel(true);
    // The legend panel will handle showing the layer info modal
  };

  const handleCloseLegendPanel = () => {
    setShowLegendPanel(false);
    setSelectedLegendLayerName(null);
  };

  // Regional AOI handlers
  const handleAOIMethodChange = (method: 'drawn' | 'regional') => {
    setAOIMethod(method);

    // Clear existing selections when switching methods
    if (method === 'drawn') {
      setHasRegionalSelection(false);
    } else {
      setHasDrawnArea(false);
      setIsDrawingMode(false);
    }

    console.log('AOI method changed to:', method);
  };

  const handleConfigureRegions = () => {
    console.log('Configure regions clicked - opening modal');
    setShowRegionalModal(true);
  };

  const handleClearRegionalSelection = () => {
    setHasRegionalSelection(false);
    setRegionalAOI(null);
    console.log('Regional selection cleared');
  };

  const handlePredefinedPolygon = (size: string) => {
    console.log('Creating predefined polygon:', size);

    // Parse size (e.g., "25x25" -> 25km x 25km)
    const [width, height] = size.split('x').map(Number);
    const sizeKm = width; // Assuming square polygons

    // Create a polygon centered on South Africa (approximate center)
    const centerLat = -29.0; // South Africa approximate center
    const centerLng = 24.0;

    // Convert km to degrees (rough approximation: 1 degree ≈ 111 km)
    const degreeOffset = (sizeKm / 2) / 111;

    const bounds = {
      north: centerLat + degreeOffset,
      south: centerLat - degreeOffset,
      east: centerLng + degreeOffset,
      west: centerLng - degreeOffset
    };

    // Calculate area in km²
    const area = sizeKm * sizeKm;

    // Set as drawn area
    setHasDrawnArea(true);
    setIsDrawingMode(false);

    // Clear regional selection if switching methods
    if (aoiMethod === 'regional') {
      setHasRegionalSelection(false);
      setRegionalAOI(null);
    }

    console.log(`Created ${size} km predefined polygon:`, { bounds, area });
  };

  const handleApplyRegionalSelection = (regions: any[], boundaryLayer: string) => {
    console.log('Applying regional selection:', { regions, boundaryLayer });

    // Calculate combined bounds from regions
    const bounds = calculateRegionalBounds(regions);
    const totalArea = calculateRegionalArea(regions);

    const regionalData = {
      method: 'regional' as const,
      boundaryLayer,
      selectedRegions: regions,
      combinedBounds: bounds,
      totalArea,
      bounds, // For compatibility with existing workflow
      area: totalArea // For compatibility with existing workflow
    };

    setRegionalAOI(regionalData);
    setHasRegionalSelection(true);
    setShowRegionalModal(false);

    console.log('Regional AOI set:', regionalData);
  };

  // Helper functions for regional bounds calculation
  const calculateRegionalBounds = (regions: any[]) => {
    // Simple bounds calculation - can be enhanced
    return {
      north: -22.0, // Mock bounds for South Africa
      south: -35.0,
      east: 33.0,
      west: 16.0
    };
  };

  const calculateRegionalArea = (regions: any[]) => {
    // Mock area calculation - sum of region areas
    return regions.length * 1000; // Mock: 1000 km² per region
  };

  useEffect(() => {
    const checkSidebarState = () => {
      const state = localStorage.getItem('sidebarCollapsed') === 'true';
      setSidebarCollapsed(state);
      console.log('Sidebar collapsed state loaded:', state);
    };

    checkSidebarState();
    
    const handleStorageChange = () => {
      checkSidebarState();
    };

    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  return (
    <div className="app-wrapper">
      <NavBar onNavigate={setCurrentView} onToolsToggle={handleToolsToggle} />
      {currentView === 'analytics' ? (
        <Container fluid className="app-container">
          <AnalyticsDashboard />
        </Container>
      ) : (
        <Container fluid className="app-container">
          <div 
            className="main-content" 
            style={{ 
              display: 'flex', 
              height: '100%', 
              width: '100%', 
              minHeight: 'calc(100vh - 60px)',
              position: 'relative'
            }}
          >
            <div
              className={`sidebar-container ${sidebarCollapsed ? 'collapsed' : ''}`}
              style={{
                transition: 'all 0.3s ease',
                height: '100%'
              }}
            >
              <Sidebar
                layers={layers}
                selectedLayerNames={selectedLayers}
                onLayerChange={handleLayerToggle}
                dateRange={dateRange}
                onDateChange={handleDateChange}
                onSearch={handleSearch}
                onPreviewData={handlePreviewData}
                onDownloadData={handleDownloadData}
                onQueryTemporalData={handleQueryTemporalData}
                isLoading={isLoading}
                error={error}
                onDrawModeToggle={handleDrawModeToggle}
                isDrawingMode={isDrawingMode}
                hasDrawnArea={hasDrawnArea}
                onClearDrawnArea={handleClearDrawnArea}
                aoiMethod={aoiMethod}
                onAOIMethodChange={handleAOIMethodChange}
                hasRegionalSelection={hasRegionalSelection}
                onConfigureRegions={handleConfigureRegions}
                onClearRegionalSelection={handleClearRegionalSelection}
                onPredefinedPolygon={handlePredefinedPolygon}
                selectedBasemap={selectedBasemap}
                onBasemapChange={handleBasemapChange}
                onShowLayerInfo={handleShowLayerInfo}
                onCoordinatePinModeToggle={handleCoordinatePinModeToggle}
                currentCoordinates={currentCoordinates}
              />
            </div>
            <div className="map-container" style={{ flex: 1, width: '100%', height: '100%' }}>
              <MapComponent
                selectedLayerNames={selectedLayers}
                dateRange={dateRange}
                onDrawComplete={setDrawnItems}
                isDrawingMode={isDrawingMode}
                onDrawModeChange={handleDrawModeToggle}
                onAOIComplete={handleAOIComplete}
                sidebarCollapsed={sidebarCollapsed}
                selectedBasemap={selectedBasemap}
                onBasemapChange={handleBasemapChange}
                legendUserMode={legendUserMode}
                onLegendUserModeChange={setLegendUserMode}
                showLegendPanel={showLegendPanel}
                onCloseLegendPanel={handleCloseLegendPanel}
                isCoordinatePinMode={coordinatePinMode}
                onCoordinateSelected={handleMapClick}
              />
            </div>
          </div>
        </Container>
      )}
      <ToolsPanel isOpen={isToolsPanelOpen} onClose={handleToolsPanelClose} />

      {/* Regional Filter Modal */}
      <RegionalFilterModal
        show={showRegionalModal}
        onHide={() => setShowRegionalModal(false)}
        onApplySelection={handleApplyRegionalSelection}
      />

      {/* Download Modal */}
      <DownloadModal
        show={showDownloadModal}
        onHide={() => setShowDownloadModal(false)}
        selectedLayers={selectedLayers}
        aoiData={regionalAOI || {
          method: 'drawn',
          area: hasDrawnArea ? 1000 : 0, // Mock area for drawn polygons
          bounds: drawnItems
        }}
        dateRange={dateRange}
      />
    </div>
  );
}

export default App;
