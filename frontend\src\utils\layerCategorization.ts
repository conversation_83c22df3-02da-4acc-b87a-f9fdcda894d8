import { LayerDiscovery } from '../types/discovery';

export type LayerCategory = 
  | 'satellite' 
  | 'flood_risk' 
  | 'historical' 
  | 'climatology' 
  | 'settlements' 
  | 'admin' 
  | 'service_points' 
  | 'other' 
  | 'basemaps';

export interface CategorizedLayers {
  satellite: LayerDiscovery[];
  flood_risk: LayerDiscovery[];
  historical: LayerDiscovery[];
  climatology: LayerDiscovery[];
  settlements: LayerDiscovery[];
  admin: LayerDiscovery[];
  service_points: LayerDiscovery[];
  other: LayerDiscovery[];
  basemaps: LayerDiscovery[];
}

/**
 * Categorize a layer based on its name, title, and abstract
 */
export const categorizeLayer = (layer: LayerDiscovery): LayerCategory => {
  const name = layer.name.toLowerCase();
  const title = layer.title.toLowerCase();
  const abstract = layer.abstract.toLowerCase();
  const keywords = layer.keywords.map(k => k.toLowerCase()).join(' ');
  const searchText = `${name} ${title} ${abstract} ${keywords}`;

  // Check authority URLs for official classification hints
  const authorityHints = layer.authorityUrls?.map(url => url.name.toLowerCase()).join(' ') || '';

  // Check identifiers for classification hints
  const identifierHints = layer.identifiers?.map(id => `${id.authority} ${id.identifier}`.toLowerCase()).join(' ') || '';

  // Combine all searchable text
  const fullSearchText = `${searchText} ${authorityHints} ${identifierHints}`;

  // Admin Layers Rule: Administrative boundaries and divisions
  if (
    // South Africa specific admin layers
    ((fullSearchText.includes('south africa') || fullSearchText.includes('south_africa')) &&
     (fullSearchText.includes('place names') ||
      fullSearchText.includes('place_names') ||
      fullSearchText.includes('boundaries') ||
      fullSearchText.includes('boundary') ||
      fullSearchText.includes('municipal') ||
      fullSearchText.includes('provincial') ||
      fullSearchText.includes('country') ||
      fullSearchText.includes('administrative') ||
      fullSearchText.includes('admin'))) ||

    // General administrative divisions (including Wards)
    fullSearchText.includes('wards') ||
    fullSearchText.includes('ward') ||
    (fullSearchText.includes('administrative') && !fullSearchText.includes('service')) ||
    (fullSearchText.includes('admin') && !fullSearchText.includes('service')) ||
    (fullSearchText.includes('boundary') || fullSearchText.includes('boundaries')) ||
    (fullSearchText.includes('municipal') && !fullSearchText.includes('service')) ||
    (fullSearchText.includes('provincial') && !fullSearchText.includes('service')) ||
    fullSearchText.includes('district') ||
    fullSearchText.includes('constituency') ||
    fullSearchText.includes('precinct') ||
    fullSearchText.includes('division') ||
    (fullSearchText.includes('electoral') && (fullSearchText.includes('ward') || fullSearchText.includes('district'))) ||

    // Keywords-based classification
    keywords.includes('administrative') ||
    keywords.includes('boundary') ||
    keywords.includes('boundaries') ||
    keywords.includes('ward') ||
    keywords.includes('wards') ||
    keywords.includes('municipal') ||
    keywords.includes('provincial') ||

    // Authority-based classification (e.g., official government sources)
    authorityHints.includes('government') ||
    authorityHints.includes('municipal') ||
    authorityHints.includes('provincial') ||
    authorityHints.includes('statistics') ||

    // Scale-based hints (admin layers often have specific scale ranges)
    (layer.minScaleDenominator && layer.maxScaleDenominator &&
     layer.minScaleDenominator > 1000 && layer.maxScaleDenominator < 10000000)
  ) {
    return 'admin';
  }

  // Satellite Data
  if (fullSearchText.includes('sentinel') ||
      fullSearchText.includes('satellite') ||
      fullSearchText.includes('cbers') ||
      fullSearchText.includes('eumetsat') ||
      fullSearchText.includes('modis') ||
      fullSearchText.includes('landsat') ||
      keywords.includes('satellite') ||
      keywords.includes('imagery') ||
      keywords.includes('remote sensing') ||
      (fullSearchText.includes('mosaic') && (fullSearchText.includes('sa_mosaic') || fullSearchText.includes('sentinel')))) {
    return 'satellite';
  }

  // Flood Risk Data - only current risk assessment layers, not historical
  if (
    // Specific flood risk indicators (current risk, not historical)
    (fullSearchText.includes('flood risk') &&
     (fullSearchText.includes('1 meter') ||
      fullSearchText.includes('3 meter') ||
      fullSearchText.includes('5 meter') ||
      fullSearchText.includes('1m') ||
      fullSearchText.includes('3m') ||
      fullSearchText.includes('5m'))) ||

    // Keywords for current flood risk
    (keywords.includes('flood risk') && !keywords.includes('historical')) ||
    (keywords.includes('inundation') && !fullSearchText.includes('historical')) ||

    // Exclude historical flood data
    (fullSearchText.includes('flood') &&
     fullSearchText.includes('risk') &&
     !fullSearchText.includes('historical') &&
     !fullSearchText.includes('historic') &&
     !fullSearchText.includes('affected') &&
     !fullSearchText.includes('plain') &&
     !fullSearchText.includes('river') &&
     !fullSearchText.includes('year') &&
     !fullSearchText.includes('water_50'))
  ) {
    return 'flood_risk';
  }

  // Historical Data - includes historical flood events and past data
  if (
    // Explicit historical indicators
    fullSearchText.includes('historical') ||
    fullSearchText.includes('historic') ||
    fullSearchText.includes('archive') ||
    keywords.includes('historical') ||
    keywords.includes('archive') ||

    // Historical flood data patterns
    (fullSearchText.includes('flood') && fullSearchText.includes('map')) ||
    fullSearchText.includes('flood_affected_structures') ||
    fullSearchText.includes('flood plain') ||
    fullSearchText.includes('floodplain') ||
    fullSearchText.includes('mthatha river flood') ||
    fullSearchText.includes('rand_water_50_year_floodline') ||

    // Time-based historical indicators
    (fullSearchText.includes('flood') &&
     (fullSearchText.includes('100 year') ||
      fullSearchText.includes('50 year') ||
      fullSearchText.includes('affected') ||
      fullSearchText.includes('structures') ||
      fullSearchText.includes('river'))) ||

    // Keywords for historical flood events
    (keywords.includes('flood') &&
     (keywords.includes('historical') ||
      keywords.includes('affected') ||
      keywords.includes('structures') ||
      keywords.includes('plain')))
  ) {
    return 'historical';
  }

  // Climatology/Meteorology
  if (fullSearchText.includes('climate') ||
      fullSearchText.includes('weather') ||
      fullSearchText.includes('meteorology') ||
      fullSearchText.includes('precipitation') ||
      fullSearchText.includes('temperature') ||
      fullSearchText.includes('rainfall') ||
      fullSearchText.includes('nasa power') ||
      fullSearchText.includes('stream flow') ||
      fullSearchText.includes('streamflow') ||
      keywords.includes('climate') ||
      keywords.includes('weather') ||
      keywords.includes('meteorology') ||
      keywords.includes('precipitation')) {
    return 'climatology';
  }

  // Human Settlements
  if (fullSearchText.includes('settlement') ||
      fullSearchText.includes('village') ||
      fullSearchText.includes('town') ||
      fullSearchText.includes('city') ||
      fullSearchText.includes('urban') ||
      fullSearchText.includes('population') ||
      fullSearchText.includes('dws village') ||
      keywords.includes('settlement') ||
      keywords.includes('urban') ||
      keywords.includes('population')) {
    return 'settlements';
  }

  // Service Points - specific service delivery points
  if (
    // Specific service types
    fullSearchText.includes('police') ||
    fullSearchText.includes('sassa') ||
    fullSearchText.includes('school') ||
    fullSearchText.includes('schools') ||
    fullSearchText.includes('ecd') ||
    fullSearchText.includes('ecds') ||
    fullSearchText.includes('early childhood development') ||
    fullSearchText.includes('community hall') ||
    fullSearchText.includes('community halls') ||
    fullSearchText.includes('clinic') ||
    fullSearchText.includes('clinics') ||
    fullSearchText.includes('health center') ||
    fullSearchText.includes('health centre') ||
    fullSearchText.includes('hospital') ||
    fullSearchText.includes('hospitals') ||

    // Keywords-based classification
    keywords.includes('police') ||
    keywords.includes('sassa') ||
    keywords.includes('school') ||
    keywords.includes('schools') ||
    keywords.includes('ecd') ||
    keywords.includes('clinic') ||
    keywords.includes('clinics') ||
    keywords.includes('hospital') ||
    keywords.includes('community hall') ||
    keywords.includes('facility') ||

    // Generic service point indicators
    fullSearchText.includes('service point') ||
    fullSearchText.includes('emergency service') ||
    (fullSearchText.includes('service') && (fullSearchText.includes('public') || fullSearchText.includes('community')))
  ) {
    return 'service_points';
  }

  // Basemaps - include mosaic layers that could serve as basemaps
  if (fullSearchText.includes('basemap') ||
      fullSearchText.includes('base map') ||
      fullSearchText.includes('elevation') ||
      fullSearchText.includes('dem') ||
      fullSearchText.includes('topography') ||
      fullSearchText.includes('ortho') ||
      fullSearchText.includes('africa_mosaic') ||
      keywords.includes('basemap') ||
      keywords.includes('elevation') ||
      keywords.includes('topography') ||
      (fullSearchText.includes('mosaic') && !fullSearchText.includes('flood'))) {
    return 'basemaps';
  }

  // Default to Other Datasets
  return 'other';
};

/**
 * Categorize all layers into their respective categories
 */
export const categorizeLayers = (layers: LayerDiscovery[]): CategorizedLayers => {
  const categorized: CategorizedLayers = {
    satellite: [],
    flood_risk: [],
    historical: [],
    climatology: [],
    settlements: [],
    admin: [],
    service_points: [],
    other: [],
    basemaps: []
  };

  layers.forEach(layer => {
    const category = categorizeLayer(layer);
    categorized[category].push(layer);
  });

  return categorized;
};

/**
 * Get layer count for a specific category
 */
export const getLayerCountForCategory = (
  categorizedLayers: CategorizedLayers, 
  category: LayerCategory,
  staticLayers: { [key: string]: any[] } = {}
): number => {
  // For categories with static layers, add them to the count
  const staticCount = staticLayers[category]?.length || 0;
  const discoveredCount = categorizedLayers[category].length;
  
  return staticCount + discoveredCount;
};
