import { Request, Response } from 'express';
import { datasetService } from '../services/datasetService';
import { DatasetSearchParams, ExportOptions } from '../types/dataset';

/**
 * Dataset Controller - Handles HTTP requests for dataset operations
 */
export class DatasetController {
  /**
   * GET /api/datasets
   * Get all datasets with optional search/filter parameters
   */
  async getAllDatasets(req: Request, res: Response) {
    try {
      const searchParams: DatasetSearchParams = {
        q: req.query.q as string,
        keywords: req.query.keywords ? (req.query.keywords as string).split(',') : undefined,
        owner: req.query.owner as string,
        category: req.query.category as string,
        subtype: req.query.subtype as string,
        date_from: req.query.date_from as string,
        date_to: req.query.date_to as string,
        bbox: req.query.bbox ? (req.query.bbox as string).split(',').map(Number) as [number, number, number, number] : undefined,
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
        offset: req.query.offset ? parseInt(req.query.offset as string) : undefined,
        sort: req.query.sort as 'title' | 'created' | 'updated' | 'popular' | 'rating',
        order: req.query.order as 'asc' | 'desc'
      };

      // If no search parameters, return all datasets
      if (!searchParams.q && !searchParams.keywords && !searchParams.owner && !searchParams.category) {
        const datasets = await datasetService.getAllDatasets();
        res.json({
          success: true,
          data: {
            datasets,
            total: datasets.length,
            page: 1,
            limit: datasets.length
          }
        });
      } else {
        // Perform search with parameters
        const result = await datasetService.searchDatasets(searchParams);
        res.json({
          success: true,
          data: result
        });
      }
    } catch (error) {
      console.error('Error in getAllDatasets:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch datasets',
        message: (error as Error).message
      });
    }
  }

  /**
   * GET /api/datasets/:id
   * Get a specific dataset by ID
   */
  async getDatasetById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const dataset = await datasetService.getDatasetById(id);
      
      if (!dataset) {
        return res.status(404).json({
          success: false,
          error: 'Dataset not found',
          message: `Dataset with ID ${id} does not exist`
        });
      }

      res.json({
        success: true,
        data: dataset
      });
    } catch (error) {
      console.error(`Error in getDatasetById for ID ${req.params.id}:`, error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch dataset',
        message: (error as Error).message
      });
    }
  }

  /**
   * GET /api/datasets/search
   * Search datasets with advanced parameters
   */
  async searchDatasets(req: Request, res: Response) {
    try {
      const searchParams: DatasetSearchParams = {
        q: req.query.q as string,
        keywords: req.query.keywords ? (req.query.keywords as string).split(',') : undefined,
        owner: req.query.owner as string,
        category: req.query.category as string,
        subtype: req.query.subtype as string,
        date_from: req.query.date_from as string,
        date_to: req.query.date_to as string,
        bbox: req.query.bbox ? (req.query.bbox as string).split(',').map(Number) as [number, number, number, number] : undefined,
        limit: req.query.limit ? parseInt(req.query.limit as string) : 20,
        offset: req.query.offset ? parseInt(req.query.offset as string) : 0,
        sort: req.query.sort as 'title' | 'created' | 'updated' | 'popular' | 'rating',
        order: req.query.order as 'asc' | 'desc'
      };

      const result = await datasetService.searchDatasets(searchParams);
      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error('Error in searchDatasets:', error);
      res.status(500).json({
        success: false,
        error: 'Search failed',
        message: (error as Error).message
      });
    }
  }

  /**
   * GET /api/datasets/:id/metadata
   * Get dataset metadata in various formats
   */
  async getDatasetMetadata(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const format = (req.query.format as string) || 'json';
      
      if (!['json', 'xml', 'iso', 'dublin-core'].includes(format)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid format',
          message: 'Supported formats: json, xml, iso, dublin-core'
        });
      }

      const metadata = await datasetService.getDatasetMetadata(id, format as any);
      
      if (format === 'json') {
        res.json({
          success: true,
          data: metadata
        });
      } else {
        res.set('Content-Type', 'application/xml');
        res.send(metadata);
      }
    } catch (error) {
      console.error(`Error in getDatasetMetadata for ID ${req.params.id}:`, error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch metadata',
        message: (error as Error).message
      });
    }
  }

  /**
   * GET /api/datasets/:id/export
   * Get export URL or redirect to download
   */
  async exportDataset(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const format = req.query.format as string || 'geojson';
      
      if (!['csv', 'excel', 'geojson', 'shapefile', 'gml', 'pdf'].includes(format)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid format',
          message: 'Supported formats: csv, excel, geojson, shapefile, gml, pdf'
        });
      }

      const dataset = await datasetService.getDatasetById(id);
      if (!dataset) {
        return res.status(404).json({
          success: false,
          error: 'Dataset not found'
        });
      }

      const exportOptions: ExportOptions = {
        format: format as any,
        bbox: req.query.bbox ? (req.query.bbox as string).split(',').map(Number) as [number, number, number, number] : undefined,
        srid: req.query.srid as string,
        attributes: req.query.attributes ? (req.query.attributes as string).split(',') : undefined
      };

      const exportUrl = datasetService.getExportUrl(dataset, exportOptions);
      
      // Option 1: Return the URL
      if (req.query.return_url === 'true') {
        res.json({
          success: true,
          data: {
            export_url: exportUrl,
            format,
            dataset_name: dataset.name,
            dataset_title: dataset.title
          }
        });
      } else {
        // Option 2: Redirect to download
        res.redirect(exportUrl);
      }
    } catch (error) {
      console.error(`Error in exportDataset for ID ${req.params.id}:`, error);
      res.status(500).json({
        success: false,
        error: 'Export failed',
        message: (error as Error).message
      });
    }
  }

  /**
   * GET /api/datasets/popular
   * Get popular datasets
   */
  async getPopularDatasets(req: Request, res: Response) {
    try {
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
      const datasets = await datasetService.getPopularDatasets(limit);
      
      res.json({
        success: true,
        data: datasets
      });
    } catch (error) {
      console.error('Error in getPopularDatasets:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch popular datasets',
        message: (error as Error).message
      });
    }
  }

  /**
   * GET /api/datasets/recent
   * Get recently updated datasets
   */
  async getRecentDatasets(req: Request, res: Response) {
    try {
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
      const datasets = await datasetService.getRecentDatasets(limit);
      
      res.json({
        success: true,
        data: datasets
      });
    } catch (error) {
      console.error('Error in getRecentDatasets:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch recent datasets',
        message: (error as Error).message
      });
    }
  }

  /**
   * GET /api/datasets/keyword/:keyword
   * Get datasets by keyword
   */
  async getDatasetsByKeyword(req: Request, res: Response) {
    try {
      const { keyword } = req.params;
      const datasets = await datasetService.getDatasetsByKeyword(keyword);
      
      res.json({
        success: true,
        data: datasets
      });
    } catch (error) {
      console.error(`Error in getDatasetsByKeyword for keyword ${req.params.keyword}:`, error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch datasets by keyword',
        message: (error as Error).message
      });
    }
  }

  /**
   * POST /api/datasets/cache/clear
   * Clear dataset cache (admin function)
   */
  async clearCache(req: Request, res: Response) {
    try {
      datasetService.clearCache();
      res.json({
        success: true,
        message: 'Cache cleared successfully'
      });
    } catch (error) {
      console.error('Error in clearCache:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to clear cache',
        message: (error as Error).message
      });
    }
  }
}

export const datasetController = new DatasetController();
