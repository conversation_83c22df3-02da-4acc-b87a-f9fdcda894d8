import React, { useState } from 'react';
import { Navbar, Nav, Button, Container } from 'react-bootstrap';
import { Home, Package, HelpCircle, FileText, LogOut, BarChart3, Settings } from 'lucide-react';
import { FEATURE_FLAGS } from '../../config';
import TimeSlider, { TemporalInfo } from '../TimeControl/TimeSlider';
import './NavBar.css';
import logo from '../../assets/logo1.png';

const NavBar: React.FC<{ 
  onNavigate?: (view: 'map' | 'analytics') => void;
  onToolsToggle?: () => void;
  temporalInfo?: TemporalInfo | null;
  onTimeChange?: (time: string) => void;
}> = ({ onNavigate, onToolsToggle, temporalInfo, onTimeChange }) => {
  // Track if temporal layer is active
  const [showTimeSlider, setShowTimeSlider] = useState(false);

  const handleNavClick = (view: 'map' | 'analytics') => {
    onNavigate?.(view);
  };

  const handleToolsClick = () => {
    onToolsToggle?.();
  };

  const handleTimeChange = (time: string) => {
    onTimeChange?.(time);
  };

  // Show time slider when temporal layer is active
  React.useEffect(() => {
    setShowTimeSlider(!!temporalInfo);
  }, [temporalInfo]);

  console.log('NavBar render: FEATURE_FLAGS.enableReporting =', FEATURE_FLAGS.enableReporting);

  return (
    <Navbar className="navbar" variant="dark" expand="lg" style={{ minHeight: 56, zIndex: 1050 }}>
      <Container fluid>
        <Nav className="align-items-center w-100">
          <Navbar.Brand href="#home" className="brand-text">
            <img
              src={logo}
              alt="SANSA Logo"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                const parent = target.parentElement!;
                // Check if fallback text already exists to prevent duplicates
                if (!parent.querySelector('.logo-fallback')) {
                  target.style.display = 'none';
                  const fallbackText = document.createElement('span');
                  fallbackText.className = 'logo-fallback';
                  fallbackText.textContent = 'SANSA';
                  fallbackText.style.fontWeight = 'bold';
                  fallbackText.style.color = 'inherit';
                  parent.appendChild(fallbackText);
                }
              }}
              style={{ height: 40, marginRight: 8 }}
            />
          </Navbar.Brand>
          <Nav.Link href="#home" onClick={() => handleNavClick('map')} data-testid="nav-home">
            <Home size={18} className="me-1" /> Home
          </Nav.Link>
          <Button variant="outline-light" size="sm" className="ms-2 discover-pill">
            Discover
          </Button>
          <div className="flex-grow-1" />
          <Nav.Link href="#products" onClick={() => handleNavClick('map')} title="Products" data-testid="nav-products">
            <Package size={18} />
          </Nav.Link>
          {FEATURE_FLAGS.enableReporting && (
            <Nav.Link href="#analytics" onClick={() => handleNavClick('analytics')} title="Analytics" data-testid="nav-analytics">
              <BarChart3 size={18} />
            </Nav.Link>
          )}
          <Nav.Link href="#help" title="Help" data-testid="nav-help">
            <HelpCircle size={18} />
          </Nav.Link>
          <Nav.Link href="#documentation" title="Documentation" data-testid="nav-docs">
            <FileText size={18} />
          </Nav.Link>
          <Nav.Link href="#tools" title="Application Tools & Settings" onClick={handleToolsClick} data-testid="nav-tools">
            <Settings size={18} />
          </Nav.Link>
          
          {/* Time Slider Component */}
          {showTimeSlider && temporalInfo && onTimeChange && (
            <div className="time-slider-wrapper">
              <TimeSlider 
                temporalInfo={temporalInfo}
                onTimeChange={handleTimeChange}
                isVisible={showTimeSlider}
              />
            </div>
          )}
          
          <Button className="logout-button" title="Logout" data-testid="nav-logout">
            <LogOut size={18} />
          </Button>
        </Nav>
      </Container>
    </Navbar>
  );
};

export default NavBar;